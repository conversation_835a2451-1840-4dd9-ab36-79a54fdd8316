package com.zrcoding.barbcker.presentation.features.settings

import org.koin.core.module.Module
import org.koin.dsl.module
import platform.Foundation.NSUserDefaults
import platform.Foundation.preferredLanguages

actual class Localization {
    actual fun changeLanguage(language: String) {
        NSUserDefaults.standardUserDefaults.setObject(
            arrayListOf(language), "AppleLanguages"
        )
    }

    actual fun getCurrentLanguage(): String {
        val appLanguages =
            NSUserDefaults.standardUserDefaults.objectForKey("AppleLanguages") as? List<*>
        if (appLanguages != null && appLanguages.isNotEmpty()) {
            return (appLanguages.firstOrNull() as? String)?.substringBefore("-") ?: "en"
        }
        // Use device preferred languages if no override was saved
        val systemLanguages = platform.Foundation.NSLocale.preferredLanguages
        return (systemLanguages.firstOrNull() as? String)?.substringBefore("-") ?: "en"
    }
}


actual val settingsModule: Module = module {
    single { Localization() }
}