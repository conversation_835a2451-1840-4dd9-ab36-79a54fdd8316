package com.zrcoding.barbcker.analytics

import org.koin.core.module.Module
import org.koin.dsl.module
import platform.Foundation.NSBundle
import kotlin.experimental.ExperimentalNativeApi

class AppConfigImpl : AppConfig {
    override val versionName: String
        get() = NSBundle.mainBundle.infoDictionary?.get("CFBundleShortVersionString") as? String
            ?: "Unknown"

    @OptIn(ExperimentalNativeApi::class)
    override val isDebug: Boolean
        get() = Platform.isDebugBinary
}

actual val platformModule: Module = module {
    single<AppConfig> { AppConfigImpl() }
}