syntax = "proto2";

package se.scmv.morocco.datastore;

message PbAccount {
  oneof account_type {
    PbNotConnectedAccount notConnected = 1;
    PbConnectedAccount connected = 2;
  }
}

message PbNotConnectedAccount {}

message PbConnectedAccount {
  required string accountId = 1;
  optional string name = 2;
  optional string email = 3;
  optional string phone = 4;
  optional string photoUrl = 5;
  required string shopName = 6;
  required string currency = 7;
}