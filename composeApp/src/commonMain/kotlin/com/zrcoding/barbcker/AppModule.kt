package com.zrcoding.barbcker

import com.zrcoding.barbcker.analytics.analyticsModule
import com.zrcoding.barbcker.data.dataModule
import com.zrcoding.barbcker.domain.domainModule
import com.zrcoding.barbcker.presentation.presentationModules
import org.koin.core.context.startKoin

val appModule = listOf(
    presentationModules,
    domainModule,
    dataModule,
    analyticsModule
)

/**
 * Initializes the Koin modules.
 */
fun initKoin() {
    startKoin {
        modules(appModule)
    }
}