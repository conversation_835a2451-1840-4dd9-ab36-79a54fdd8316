package com.zrcoding.barbcker.data.database.daos

import androidx.room.Dao
import androidx.room.Query
import app.cash.paging.PagingSource
import com.zrcoding.barbcker.data.database.daos_results.BarberRevenueResult
import kotlinx.coroutines.flow.Flow

@Dao
interface StatsDao {

    /**
     * Get the total gross revenue from haircuts for a given period.
     *
     * The sum is calculated by adding up the `price` field for all haircuts within the time range.
     * Returns 0.0 if there are no matching records.
     *
     * @param startTimestamp Start of the period (inclusive) in milliseconds since epoch.
     * @param endTimestamp End of the period (inclusive) in milliseconds since epoch.
     * @return A Flow emitting the total revenue as a Double.
     */
    @Query(
        """
        SELECT COALESCE(SUM(price), 0.0)
        FROM haircuts
        WHERE created_at >= :startTimestamp AND created_at <= :endTimestamp
    """
    )
    fun getTotalRevenue(startTimestamp: Long, endTimestamp: Long): Flow<Double>

    /**
     * Get the total net revenue from haircuts after commissions for a given period.
     *
     * The sum is calculated by adding up `price * (1 - commission_rate / 100.0)` for each haircut within the time range.
     * This represents the revenue after subtracting the barber's commission.
     * Returns 0.0 if there are no matching records.
     *
     * @param startTimestamp Start of the period (inclusive) in milliseconds since epoch.
     * @param endTimestamp End of the period (inclusive) in milliseconds since epoch.
     * @return A Flow emitting the total net revenue as a Double.
     */
    @Query(
        """
        SELECT COALESCE(SUM(price * (1 - commission_rate / 100.0)), 0.0)
        FROM haircuts
        WHERE created_at >= :startTimestamp AND created_at <= :endTimestamp
    """
    )
    fun getShopOwnerTotalRevenueForPeriod(startTimestamp: Long, endTimestamp: Long): Flow<Double>

    /**
     * Get total tips for the barbers(sum of all tips from haircuts)
     *
     * @param startTimestamp Start of the period (inclusive) in milliseconds
     * @param endTimestamp End of the period (inclusive) in milliseconds
     */
    @Query(
        """
        SELECT COALESCE(SUM(tip), 0.0)
        FROM haircuts
        WHERE created_at >= :startTimestamp AND created_at <= :endTimestamp
    """
    )
    fun getTotalTips(startTimestamp: Long, endTimestamp: Long): Flow<Double>

    /**
     * Get barber revenue statistics including:
     * - Barber information
     * - Total number of haircuts
     * - Total revenue earned by barber (what barber gets after commission)
     * - Total tips received
     *
     * Revenue calculation for barber: (price + tip) * (1 - commission_rate / 100.0)
     *
     * @param startTimestamp Start of the period (inclusive) in milliseconds
     * @param endTimestamp End of the period (inclusive) in milliseconds
     */
    @Query(
        """
        SELECT
            b.uuid as barber_uuid,
            b.name as barber_name,
            b.commission_rate as barber_commission_rate,
            b.phone_number as barber_phone_number,
            COALESCE(COUNT(h.uuid), 0) as total_haircuts,
            COALESCE(SUM(price * h.commission_rate / 100.0), 0.0) as total_revenue,
            COALESCE(SUM(h.tip), 0.0) as total_tips
        FROM barbers b
        LEFT JOIN haircuts h ON b.uuid = h.barber_uuid
            AND h.created_at >= :startTimestamp
            AND h.created_at <= :endTimestamp
            WHERE b.is_deleted = 0
        GROUP BY b.uuid, b.name, b.commission_rate, b.phone_number
        ORDER BY total_revenue DESC
    """
    )
    fun getBarbersRevenue(
        startTimestamp: Long,
        endTimestamp: Long
    ): PagingSource<Int, BarberRevenueResult>
}