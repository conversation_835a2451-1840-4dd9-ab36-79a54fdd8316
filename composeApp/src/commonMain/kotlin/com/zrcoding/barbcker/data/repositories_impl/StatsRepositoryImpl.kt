package com.zrcoding.barbcker.data.repositories_impl

import app.cash.paging.Pager
import app.cash.paging.PagingConfig
import app.cash.paging.PagingData
import app.cash.paging.map
import com.zrcoding.barbcker.data.database.AppDatabase
import com.zrcoding.barbcker.data.mappers.toBarberRevenue
import com.zrcoding.barbcker.domain.models.BarberRevenue
import com.zrcoding.barbcker.domain.repositories.StatsRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class StatsRepositoryImpl(
    private val appDatabase: AppDatabase
) : StatsRepository {

    override fun getTotalRevenueForPeriod(startTimestamp: Long, endTimestamp: Long): Flow<Double> {
        return appDatabase.statsDao().getTotalRevenue(startTimestamp, endTimestamp)
    }

    override fun getShopOwnerTotalRevenueForPeriod(
        startTimestamp: Long,
        endTimestamp: Long
    ): Flow<Double> {
        return appDatabase.statsDao()
            .getShopOwnerTotalRevenueForPeriod(startTimestamp, endTimestamp)
    }

    override fun getTotalTipsForPeriod(startTimestamp: Long, endTimestamp: Long): Flow<Double> {
        return appDatabase.statsDao().getTotalTips(startTimestamp, endTimestamp)
    }

    override fun getBarbersRevenueForPeriod(
        startTimestamp: Long,
        endTimestamp: Long
    ): Flow<PagingData<BarberRevenue>> {
        return Pager(
            config = PagingConfig(pageSize = 20),
            pagingSourceFactory = {
                appDatabase.statsDao().getBarbersRevenue(startTimestamp, endTimestamp)
            }
        ).flow.map { pagingData ->
            pagingData.map { it.toBarberRevenue() }
        }
    }
}