package com.zrcoding.barbcker.data.repositories_impl

import app.cash.paging.Pager
import app.cash.paging.PagingConfig
import app.cash.paging.PagingData
import app.cash.paging.map
import com.zrcoding.barbcker.data.database.AppDatabase
import com.zrcoding.barbcker.data.dtos.Constants
import com.zrcoding.barbcker.data.mappers.toBarber
import com.zrcoding.barbcker.data.mappers.toBarberDto
import com.zrcoding.barbcker.data.mappers.toBarberEntity
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.domain.repositories.BarberRepository
import dev.gitlive.firebase.auth.FirebaseAuth
import dev.gitlive.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid

class BarberRepositoryImpl(
    private val appDatabase: AppDatabase,
    private val firebaseAuth: FirebaseAuth,
    private val firebaseFirestore: FirebaseFirestore,
) : BarberRepository {
    @OptIn(ExperimentalUuidApi::class)
    override suspend fun insert(name: String, commissionRate: Int, phoneNumber: String) {
        val barber = Barber(
            uuid = Uuid.random().toString(),
            name = name,
            commissionRate = commissionRate,
            phoneNumber = phoneNumber
        )
        appDatabase.barberDao().insertOne(barber.toBarberEntity())
    }

    override suspend fun update(
        uuid: String,
        name: String,
        commissionRate: Int,
        phoneNumber: String
    ) {
        appDatabase.barberDao().updateOne(
            uuid = uuid,
            name = name,
            commissionRate = commissionRate,
            phoneNumber = phoneNumber
        )
        syncBarbers()
    }

    override suspend fun delete(uuid: String) {
        appDatabase.barberDao().deleteOne(uuid)
        syncBarbers()
    }

    override suspend fun getOne(uuid: String): Barber? {
        return appDatabase.barberDao().getOne(uuid)?.toBarber()
    }

    override fun observeAll(): Flow<PagingData<Barber>> {
        return Pager(
            config = PagingConfig(pageSize = 20),
            pagingSourceFactory = {
                appDatabase.barberDao().observeAll()
            }
        ).flow.map { pagingData ->
            pagingData.map { it.toBarber() }
        }
    }

    override suspend fun getAll(): List<Barber> {
        return appDatabase.barberDao().getAll().map { it.toBarber() }
    }

    override suspend fun getCount(): Int {
        return appDatabase.barberDao().getCount()
    }

    // TODO Check how ty sync only changes not all the table
    override suspend fun syncBarbers() {
        return try {
            val user = firebaseAuth.currentUser ?: throw Exception("User not logged in")
            val barbers = appDatabase.barberDao().getAll().map { it.toBarberDto() }
            val barbersDto = hashMapOf(Constants.BARBERS to barbers)
            firebaseFirestore.collection(Constants.USERS_COLLECTION)
                .document(documentPath = user.uid)
                .set(data = barbersDto, merge = true)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override suspend fun clear() {
        appDatabase.barberDao().clear()
    }
}
