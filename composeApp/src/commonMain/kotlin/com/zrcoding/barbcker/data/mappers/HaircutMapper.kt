package com.zrcoding.barbcker.data.mappers

import com.zrcoding.barbcker.data.database.entities.HaircutEntity
import com.zrcoding.barbcker.domain.models.Haircut
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import kotlin.time.ExperimentalTime
import kotlin.time.Instant

@OptIn(ExperimentalTime::class)
fun HaircutEntity.toHaircut() = Haircut(
    uuid = uuid,
    price = price,
    tip = tip,
    commissionRate = commissionRate,
    createAt = Instant
        .fromEpochMilliseconds(createdAt)
        .toLocalDateTime(TimeZone.currentSystemDefault())
)