package com.zrcoding.barbcker.data.repositories_impl

import com.tweener.passage.Passage
import com.tweener.passage.error.PassageEmailAddressAlreadyExistsException
import com.tweener.passage.error.PassageInvalidCredentialsException
import com.tweener.passage.error.PassageNoUserMatchingEmailException
import com.tweener.passage.gatekeeper.apple.error.PassageAppleGatekeeperException
import com.tweener.passage.gatekeeper.email.model.PassageEmailAuthParams
import com.tweener.passage.gatekeeper.email.model.PassageForgotPasswordAndroidParams
import com.tweener.passage.gatekeeper.email.model.PassageForgotPasswordIosParams
import com.tweener.passage.gatekeeper.email.model.PassageForgotPasswordParams
import com.tweener.passage.gatekeeper.google.error.PassageGoogleGatekeeperException
import com.tweener.passage.model.Entrant
import com.zrcoding.barbcker.data.database.AppDatabase
import com.zrcoding.barbcker.data.dtos.AccountDto
import com.zrcoding.barbcker.data.dtos.Constants
import com.zrcoding.barbcker.data.mappers.toBarberEntity
import com.zrcoding.barbcker.domain.models.NetworkErrors
import com.zrcoding.barbcker.domain.models.ResetPasswordErrors
import com.zrcoding.barbcker.domain.models.Resource
import com.zrcoding.barbcker.domain.models.SignInErrors
import com.zrcoding.barbcker.domain.models.SignInStatus
import com.zrcoding.barbcker.domain.models.SignUpErrors
import com.zrcoding.barbcker.domain.models.failure
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import com.zrcoding.barbcker.domain.repositories.AuthRepository
import dev.gitlive.firebase.FirebaseNetworkException
import dev.gitlive.firebase.auth.FirebaseAuth
import dev.gitlive.firebase.firestore.FirebaseFirestore

class AuthRepositoryImpl(
    private val passage: Passage,
    private val firebaseFirestore: FirebaseFirestore,
    private val firebaseAuth: FirebaseAuth,
    private val accountRepository: AccountRepository,
    private val appDatabase: AppDatabase,
) : AuthRepository {

    override suspend fun authenticateWithEmailAndPassword(
        email: String,
        password: String
    ): Resource<SignInStatus, SignInErrors> {
        val emailPasswordParams = PassageEmailAuthParams(email = email, password = password)
        return authenticate { passage.authenticateWithEmailAndPassword(emailPasswordParams) }
    }

    override suspend fun authenticateWithGoogle(): Resource<SignInStatus, SignInErrors> {
        return authenticate { passage.authenticateWithGoogle() }
    }

    override suspend fun authenticateWithApple(): Resource<SignInStatus, SignInErrors> {
        return authenticate { passage.authenticateWithApple() }
    }

    private suspend fun authenticate(
        authMethod: suspend () -> Result<Entrant>
    ): Resource<SignInStatus, SignInErrors> {
        return try {
            val entrant = authMethod().getOrThrow()
            val accountDto = firebaseFirestore
                .collection(Constants.USERS_COLLECTION)
                .document(entrant.uid)
                .get()
            val completionStatus = if (accountDto.exists) {
                val (shopInfo, barbers) = accountDto.data(strategy = AccountDto.serializer())
                if (shopInfo == null) {
                    accountRepository.saveAccount(entrant = entrant)
                    SignInStatus.CompletionStatus.SHOULD_COMPLETE_ACCOUNT
                } else {
                    accountRepository.saveAccount(
                        entrant = entrant,
                        shopName = shopInfo.shopName,
                        currency = shopInfo.currency
                    )
                    if (barbers.isEmpty()) {
                        SignInStatus.CompletionStatus.SHOULD_SETUP_BARBERS
                    } else {
                        appDatabase.barberDao().insertMany(
                            barbers = barbers.map { it.toBarberEntity() }
                        )
                        SignInStatus.CompletionStatus.COMPLETED
                    }
                }
            } else {
                accountRepository.saveAccount(entrant = entrant)
                SignInStatus.CompletionStatus.SHOULD_COMPLETE_ACCOUNT
            }
            Resource.Success(
                SignInStatus(
                    uid = entrant.uid,
                    name = entrant.displayName.orEmpty(),
                    email = entrant.email.orEmpty(),
                    phoneNumber = entrant.phoneNumber.orEmpty(),
                    completionStatus = completionStatus
                )
            )
        } catch (e: PassageInvalidCredentialsException) {
            e.printStackTrace()
            Resource.Failure(SignInErrors.InvalidCredentials)
        } catch (e: PassageGoogleGatekeeperException) {
            e.printStackTrace()
            Resource.Failure(SignInErrors.Cancelled)
        } catch (e: PassageAppleGatekeeperException) {
            e.printStackTrace()
            Resource.Failure(SignInErrors.Cancelled)
        } catch (e: FirebaseNetworkException) {
            e.printStackTrace()
            Resource.Failure(SignInErrors.Network(NetworkErrors.NO_INTERNET))
        } catch (e: Exception) {
            e.printStackTrace()
            Resource.Failure(SignInErrors.Network(NetworkErrors.UNKNOWN))
        }
    }

    override suspend fun signUpWithEmailAndPassword(
        email: String,
        password: String
    ): Resource<Unit, SignUpErrors> {
        val emailPasswordParams = PassageEmailAuthParams(email = email, password = password)
        return passage.createUserWithEmailAndPassword(emailPasswordParams).fold(
            onSuccess = { entrant ->
                accountRepository.saveAccount(entrant = entrant)
                Resource.Success(Unit)
            },
            onFailure = {
                return when (it) {
                    is PassageEmailAddressAlreadyExistsException -> failure(SignUpErrors.EmailAlreadyExists)
                    else -> failure(SignUpErrors.Network(NetworkErrors.UNKNOWN))
                }
            }
        )
    }

    override suspend fun sendPasswordResetEmail(
        email: String
    ): Resource<Unit, ResetPasswordErrors> {
        val param = PassageForgotPasswordParams(
            email = email,
            url = "https://barbcker.firebaseapp.com/action/password_reset",
            iosParams = PassageForgotPasswordIosParams(bundleId = "com.zrcoding.barbcker"),
            androidParams = PassageForgotPasswordAndroidParams(
                packageName = "com.zrcoding.barbcker",
            ),
            canHandleCodeInApp = true,
        )
        return passage.sendPasswordResetEmail(param).fold(
            onSuccess = { Resource.Success(Unit) },
            onFailure = {
                return when (it) {
                    is PassageNoUserMatchingEmailException -> failure(ResetPasswordErrors.EmailDoesntExists)
                    is FirebaseNetworkException -> failure(ResetPasswordErrors.Network(NetworkErrors.NO_INTERNET))
                    else -> failure(ResetPasswordErrors.Network(NetworkErrors.UNKNOWN))
                }
            }
        )
    }
}