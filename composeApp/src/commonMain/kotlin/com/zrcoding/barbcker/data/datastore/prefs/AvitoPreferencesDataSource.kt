package com.zrcoding.barbcker.data.datastore.prefs

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map

class PreferencesDataSource(
    private val dataStore: DataStore<Preferences>
) {
    suspend fun saveString(key: String, value: String) {
        dataStore.edit { prefs ->
            prefs[stringPreferencesKey(key)] = value
        }
    }

    fun observeString(key: String): Flow<String?> {
        return dataStore.data.map { prefs ->
            prefs[stringPreferencesKey(key)]
        }
    }

    suspend fun getString(key: String): String? {
        return dataStore.data.map { prefs ->
            prefs[stringPreferencesKey(key)]
        }.firstOrNull()
    }

    suspend fun getString(key: String, defaultValue: String): String {
        val prefValue = dataStore.data.map { prefs ->
            prefs[stringPreferencesKey(key)]
        }.firstOrNull()
        return prefValue ?: defaultValue
    }

    suspend fun getBoolean(key: String, defaultValue: Boolean): Boolean {
        val prefValue = dataStore.data.map { prefs ->
            prefs[stringPreferencesKey(key)]
        }.firstOrNull()
        return prefValue?.toBoolean() ?: defaultValue
    }

    suspend fun saveBoolean(key: String, value: Boolean) {
        dataStore.edit { prefs ->
            prefs[stringPreferencesKey(key)] = value.toString()
        }
    }

    suspend fun delete(key: String) {
        dataStore.edit { prefs ->
            prefs.remove(stringPreferencesKey(key))
        }
    }
}