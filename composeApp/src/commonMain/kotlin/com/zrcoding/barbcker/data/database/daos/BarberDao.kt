package com.zrcoding.barbcker.data.database.daos

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import app.cash.paging.PagingSource
import com.zrcoding.barbcker.data.database.entities.BarberEntity

@Dao
interface BarberDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOne(barber: BarberEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMany(barbers: List<BarberEntity>)

    @Query("UPDATE barbers SET name = :name, commission_rate = :commissionRate, phone_number = :phoneNumber WHERE uuid = :uuid")
    suspend fun updateOne(uuid: String, name: String, commissionRate: Int, phoneNumber: String)

    @Query("SELECT * FROM barbers WHERE uuid = :uuid")
    suspend fun getOne(uuid: String): BarberEntity?

    @Query("SELECT * FROM barbers WHERE is_deleted = 0")
    suspend fun getAll(): List<BarberEntity>

    @Query("SELECT COUNT(*) FROM barbers WHERE is_deleted = 0")
    suspend fun getCount(): Int

    @Query("SELECT * FROM barbers WHERE is_deleted = 0")
    fun observeAll(): PagingSource<Int, BarberEntity>

    @Query("UPDATE barbers SET is_deleted = 1 WHERE uuid = :uuid")
    suspend fun deleteOne(uuid: String)

    @Query("DELETE FROM barbers")
    suspend fun clear()
}


