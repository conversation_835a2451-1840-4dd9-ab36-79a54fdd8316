package com.zrcoding.barbcker.data.database.daos

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import app.cash.paging.PagingSource
import com.zrcoding.barbcker.data.database.entities.HaircutEntity
import com.zrcoding.barbcker.data.database.entities.HaircutWithBarberEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface HaircutDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOne(barber: HaircutEntity)

    @Transaction
    @Query("SELECT * FROM haircuts ORDER BY created_at DESC")
    fun observeAll(): PagingSource<Int, HaircutWithBarberEntity>

    @Query("SELECT * FROM haircuts WHERE barber_uuid = :barberId ORDER BY created_at DESC")
    fun observeBarberHaircuts(barberId: String): PagingSource<Int, HaircutWithBarberEntity>

    @Transaction
    @Query(
        """
        SELECT * FROM haircuts
        ORDER BY created_at DESC
        LIMIT :limit
        """
    )
    fun getLatestHaircuts(limit: Int): Flow<List<HaircutWithBarberEntity>>
}


