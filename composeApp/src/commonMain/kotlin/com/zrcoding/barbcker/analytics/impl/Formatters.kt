package com.zrcoding.barbcker.analytics.impl

import com.zrcoding.barbcker.analytics.models.Param


/**
 * Converts a set of [Param] objects into a [Map].
 *
 * This extension function iterates over each [Param] in the set and adds it to the
 * [Map] as a key-value pair, where the key is the param's name and the value is
 * its corresponding value.
 *
 * @return A [Map] containing all the parameters as key-value pairs.
 */
fun Set<Param>.toMap() = mutableMapOf<String, String>().apply {
    <EMAIL> { param ->
        set(param.key, param.value)
    }
}