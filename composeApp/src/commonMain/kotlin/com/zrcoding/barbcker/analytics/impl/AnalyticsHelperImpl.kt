package com.zrcoding.barbcker.analytics.impl

import com.zrcoding.barbcker.analytics.AnalyticsHelper
import com.zrcoding.barbcker.analytics.models.AnalyticsEvent
import com.zrcoding.barbcker.analytics.models.UserProperties
import dev.gitlive.firebase.analytics.FirebaseAnalytics

/**
 * Implementation of the [AnalyticsHelper] interface for logging events and managing user data
 * on Firebase.
 *
 * @property firebaseAnalytics The Firebase Analytics instance for logging events and user properties.
 */
internal class AnalyticsHelperImpl(
    private val firebaseAnalytics: FirebaseAnalytics,
) : AnalyticsHelper {

    override fun logEvent(event: AnalyticsEvent) {
        firebaseAnalytics.logEvent(
            event.name,
            event.properties.toMap()
        )
    }

    override fun setUserProperties(properties: UserProperties) {
        firebaseAnalytics.setUserId(id = properties.uid)
        firebaseAnalytics.setUserProperty(name = "email", value = properties.email)
        firebaseAnalytics.setUserProperty(name = "name", value = properties.name)
        firebaseAnalytics.setUserProperty(name = "phone", value = properties.phone)
    }

    override fun clearUserData() {
        firebaseAnalytics.resetAnalyticsData()
    }
}