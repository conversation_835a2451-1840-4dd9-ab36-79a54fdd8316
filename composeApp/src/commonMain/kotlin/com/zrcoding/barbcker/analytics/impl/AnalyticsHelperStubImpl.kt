package com.zrcoding.barbcker.analytics.impl

import com.zrcoding.barbcker.analytics.AnalyticsHelper
import com.zrcoding.barbcker.analytics.models.AnalyticsEvent
import com.zrcoding.barbcker.analytics.models.UserProperties
import io.github.aakira.napier.Napier

private const val TAG = "StubAnalyticsHelper"

/**
 * Debug AnalyticsHelper which logs events in the console.
 */
internal class AnalyticsHelperStubImpl : AnalyticsHelper {

    override fun logEvent(event: AnalyticsEvent) {
        Napier.d(message = "Log analytics: $event", tag = TAG)
    }

    override fun setUserProperties(properties: UserProperties) {}

    override fun clearUserData() {}
}