package com.zrcoding.barbcker.analytics

import com.zrcoding.barbcker.analytics.impl.AnalyticsHelperImpl
import dev.gitlive.firebase.Firebase
import dev.gitlive.firebase.analytics.FirebaseAnalytics
import dev.gitlive.firebase.analytics.analytics
import org.koin.core.module.Module
import org.koin.dsl.module

interface AppConfig {
    val versionName: String
    val isDebug: <PERSON>olean
}

val analyticsModule: Module = module {
    single<FirebaseAnalytics> { Firebase.analytics }
    single<AnalyticsHelper> {
        AnalyticsHelperImpl(get())
    }
    includes(platformModule)
}

expect val platformModule: Module