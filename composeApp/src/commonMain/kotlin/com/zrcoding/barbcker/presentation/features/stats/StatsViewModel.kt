package com.zrcoding.barbcker.presentation.features.stats

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import app.cash.paging.PagingData
import app.cash.paging.map
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import com.zrcoding.barbcker.domain.repositories.StatsRepository
import com.zrcoding.barbcker.domain.utils.DateUtils
import com.zrcoding.barbcker.domain.utils.DateUtils.getEndDateOfMonth
import com.zrcoding.barbcker.domain.utils.DateUtils.getStartOfMonth
import com.zrcoding.barbcker.presentation.common.extension.round
import com.zrcoding.barbcker.presentation.features.common.model.BarberRevenueUiModel
import com.zrcoding.barbcker.presentation.features.common.model.toUiModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atStartOfDayIn
import kotlinx.datetime.atTime
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import kotlin.time.ExperimentalTime
import kotlin.time.Instant

@OptIn(ExperimentalCoroutinesApi::class)
class StatsViewModel(
    private val statsRepository: StatsRepository,
    private val accountRepository: AccountRepository,
) : ViewModel() {

    val listOfMonths = DateUtils.generateMonths()

    @OptIn(ExperimentalTime::class)
    private val currentMonthRange = DateUtils.getThisMonthRange()

    private val periodEvents = MutableStateFlow(currentMonthRange)

    val barbersRevenue: Flow<PagingData<BarberRevenueUiModel>> = combine(
        flow = periodEvents,
        flow2 = accountRepository.getAccount(),
        transform = { period, account ->
            Pair(period, account)
        }
    ).flatMapLatest { (period, account) ->
        statsRepository.getBarbersRevenueForPeriod(period.first, period.second)
            .map { pagingData ->
                // TODO Call logout if account is not connected
                val currency = ((account as? Account.Connected)?.currency ?: Currency.DOLLAR).symbol
                pagingData.map { it.toUiModel(currencySymbol = currency) }
            }
    }

    private val _viewState = MutableStateFlow(
        StatsViewState(
            monthsFilter = listOfMonths,
            selectedMonth = listOfMonths.first(),
            selectedStartDateMillis = currentMonthRange.first,
            selectedEndDateMillis = currentMonthRange.second,
        )
    )
    val viewState = _viewState.asStateFlow()

    init {
        viewModelScope.launch {
            periodEvents.flatMapLatest { period ->
                _viewState.update { it }
                combine(
                    flow = accountRepository.getAccount(),
                    flow2 = statsRepository.getTotalRevenueForPeriod(
                        startTimestamp = period.first,
                        endTimestamp = period.second
                    ),
                    flow3 = statsRepository.getShopOwnerTotalRevenueForPeriod(
                        startTimestamp = period.first,
                        endTimestamp = period.second
                    ),
                    flow4 = statsRepository.getTotalTipsForPeriod(
                        startTimestamp = period.first,
                        endTimestamp = period.second
                    ),
                    flow5 = statsRepository.getBarbersRevenueForPeriod(
                        startTimestamp = period.first,
                        endTimestamp = period.second
                    )
                ) { account, totalRevenue, shopOwnerTotalRevenue, totalTips, barbersRevenue ->
                    // TODO Call logout if account is not connected
                    val currencySymbol =
                        ((account as? Account.Connected)?.currency ?: Currency.DOLLAR).symbol
                    _viewState.value.copy(
                        totalRevenue = "${totalRevenue.round()} $currencySymbol",
                        shopOwnerTotalRevenue = "${shopOwnerTotalRevenue.round()} $currencySymbol",
                        totalTips = "${totalTips.round()} $currencySymbol",
                    )
                }
            }.collectLatest { state -> _viewState.update { state } }
        }
    }

    fun onMonthFilterChanged(monthFilter: MonthFilter) {
        viewModelScope.launch {
            _viewState.update {
                it.copy(
                    selectedMonth = monthFilter,
                    selectedStartDateMillis = monthFilter.localDateTime.getStartOfMonth(),
                    selectedEndDateMillis = monthFilter.localDateTime.getEndDateOfMonth()
                )
            }
            periodEvents.emit(
                Pair(
                    monthFilter.localDateTime.getStartOfMonth(),
                    monthFilter.localDateTime.getEndDateOfMonth()
                )
            )
        }
    }

    @OptIn(ExperimentalTime::class)
    fun onDateRangeChanged(startDate: Long?, endDate: Long?) {
        if (startDate == null || endDate == null) return
        val startDateLocalDate = Instant.fromEpochMilliseconds(startDate)
            .toLocalDateTime(TimeZone.UTC)
            .date
        val endDateLocalDate = Instant.fromEpochMilliseconds(endDate)
            .toLocalDateTime(TimeZone.UTC)
            .date
        val monthFilter = viewState.value.monthsFilter.firstOrNull {
            it.localDateTime.month == startDateLocalDate.month && it.localDateTime.month == endDateLocalDate.month
        }
        viewModelScope.launch {
            _viewState.update {
                it.copy(
                    selectedMonth = monthFilter,
                    selectedStartDateMillis = startDate,
                    selectedEndDateMillis = endDate
                )
            }
            periodEvents.emit(
                Pair(
                    first = Instant.fromEpochMilliseconds(startDate)
                        .toLocalDateTime(TimeZone.UTC)
                        .date
                        .atStartOfDayIn(TimeZone.UTC)
                        .toEpochMilliseconds(),
                    second = Instant.fromEpochMilliseconds(endDate)
                        .toLocalDateTime(TimeZone.UTC)
                        .date
                        .atTime(23, 59, 59, 999_999_999)
                        .toInstant(TimeZone.UTC)
                        .toEpochMilliseconds()
                )
            )
        }
    }
}