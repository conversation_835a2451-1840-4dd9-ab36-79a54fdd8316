package com.zrcoding.barbcker.presentation.features.haircut.list

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import app.cash.paging.compose.LazyPagingItems
import app.cash.paging.compose.collectAsLazyPagingItems
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.haircut_list_empty_state
import barbcker.composeapp.generated.resources.haircut_list_empty_state_description
import barbcker.composeapp.generated.resources.haircut_list_title
import barbcker.composeapp.generated.resources.img_hourglass
import com.zrcoding.barbcker.presentation.common.extension.isEmpty
import com.zrcoding.barbcker.presentation.design_system.components.BcTopAppBar
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import com.zrcoding.barbcker.presentation.features.common.components.HaircutItem
import com.zrcoding.barbcker.presentation.features.common.model.HaircutWithBarberUiModel
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun HaircutListRoute(
    navigateBack: () -> Unit,
    viewModel: HaircutListViewModel = koinViewModel()
) {
    val haircuts = viewModel.haircuts.collectAsLazyPagingItems()
    HaircutListScreen(
        navigateBack = navigateBack,
        haircuts = haircuts,
    )
}

@Composable
private fun HaircutListScreen(
    navigateBack: () -> Unit,
    haircuts: LazyPagingItems<HaircutWithBarberUiModel>,
) {
    Column(modifier = Modifier.fillMaxSize()) {
        BcTopAppBar(
            onNavigationIconClicked = navigateBack,
            title = stringResource(Res.string.haircut_list_title)
        )
        when {
            haircuts.isEmpty() -> HaircutListEmptyState()
            else -> LazyColumn(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.large),
                contentPadding = PaddingValues(
                    horizontal = MaterialTheme.dimension.screenPaddingHorizontal,
                    vertical = MaterialTheme.dimension.big
                )
            ) {
                items(haircuts.itemCount) { index ->
                    haircuts[index]?.let { haircut ->
                        HaircutItem(haircut = haircut)
                    }
                }
            }
        }
    }
}

@Composable
private fun HaircutListEmptyState() {
    Column(
        modifier = Modifier.fillMaxWidth().padding(top = MaterialTheme.dimension.extraBig),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(
            modifier = Modifier.width(120.dp),
            painter = painterResource(Res.drawable.img_hourglass),
            contentDescription = null,
            contentScale = ContentScale.Fit
        )
        Text(
            text = stringResource(Res.string.haircut_list_empty_state),
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center
        )
        Text(
            text = stringResource(Res.string.haircut_list_empty_state_description),
            style = MaterialTheme.typography.labelMedium,
            textAlign = TextAlign.Center
        )
    }
}