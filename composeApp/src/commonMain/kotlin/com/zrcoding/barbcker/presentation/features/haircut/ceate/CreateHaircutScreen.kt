package com.zrcoding.barbcker.presentation.features.haircut.ceate

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.common_save
import barbcker.composeapp.generated.resources.create_haircut_barber_label
import barbcker.composeapp.generated.resources.create_haircut_barber_placeholder
import barbcker.composeapp.generated.resources.create_haircut_calculations
import barbcker.composeapp.generated.resources.create_haircut_commission
import barbcker.composeapp.generated.resources.create_haircut_commission_description
import barbcker.composeapp.generated.resources.create_haircut_price_label
import barbcker.composeapp.generated.resources.create_haircut_price_placeholder
import barbcker.composeapp.generated.resources.create_haircut_shop_owner_total
import barbcker.composeapp.generated.resources.create_haircut_shop_owner_total_description
import barbcker.composeapp.generated.resources.create_haircut_tip_label
import barbcker.composeapp.generated.resources.create_haircut_tip_placeholder
import barbcker.composeapp.generated.resources.create_haircut_title
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.presentation.design_system.components.BcDropdown
import com.zrcoding.barbcker.presentation.design_system.components.BcPrimaryButton
import com.zrcoding.barbcker.presentation.design_system.components.BcTextField
import com.zrcoding.barbcker.presentation.design_system.components.BcTopAppBar
import com.zrcoding.barbcker.presentation.design_system.theme.BarbckerTheme
import com.zrcoding.barbcker.presentation.design_system.theme.Green700
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.collectLatest
import org.jetbrains.compose.resources.StringResource
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun CreateHaircutRoute(
    navigateBack: () -> Unit,
    viewModel: CreateHaircutViewModel = koinViewModel()
) {
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    CreateHaircutScreen(
        viewState = viewState,
        navigateBack = navigateBack,
        onBarberSelected = viewModel::onBarberSelected,
        onPriceChanged = viewModel::onPriceChanged,
        onTipChanged = viewModel::onTipChanged,
        onSubmit = viewModel::onSubmit,
    )
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvent.collectLatest {
            when (it) {
                CreateHaircutOneTimeEvents.Close -> navigateBack()
            }
        }
    }
}

@Composable
private fun CreateHaircutScreen(
    viewState: CreateHaircutViewState,
    navigateBack: () -> Unit,
    onBarberSelected: (Barber) -> Unit,
    onPriceChanged: (String) -> Unit,
    onTipChanged: (String) -> Unit,
    onSubmit: () -> Unit,
) {
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            BcTopAppBar(
                onNavigationIconClicked = navigateBack,
                title = stringResource(Res.string.create_haircut_title)
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal)
                .padding(vertical = MaterialTheme.dimension.big),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.large)
        ) {
            BcDropdown(
                modifier = Modifier.fillMaxWidth(),
                items = viewState.barbers,
                selectedItem = viewState.barber,
                key = { it.uuid },
                name = { "${it.name}    ${it.commissionRate}%" },
                onItemSelected = onBarberSelected,
                required = true,
                title = stringResource(Res.string.create_haircut_barber_label),
                placeholder = Res.string.create_haircut_barber_placeholder,
                error = viewState.priceError?.let { stringResource(it) },
            )
            BcTextField(
                modifier = Modifier.fillMaxWidth(),
                value = viewState.price,
                onValueChanged = onPriceChanged,
                required = true,
                title = Res.string.create_haircut_price_label,
                placeholder = Res.string.create_haircut_price_placeholder,
                error = viewState.priceError?.let { stringResource(it) },
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Next
                ),
                singleLine = true
            )
            BcTextField(
                modifier = Modifier.fillMaxWidth(),
                value = viewState.tip,
                onValueChanged = onTipChanged,
                title = Res.string.create_haircut_tip_label,
                placeholder = Res.string.create_haircut_tip_placeholder,
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Done
                ),
                singleLine = true
            )
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.large)
            ) {
                Text(
                    text = stringResource(Res.string.create_haircut_calculations),
                    style = MaterialTheme.typography.titleMedium,
                )
                CalculationItem(
                    label = Res.string.create_haircut_shop_owner_total,
                    description = Res.string.create_haircut_shop_owner_total_description,
                    amount = viewState.receivedAmount,
                )
                CalculationItem(
                    label = Res.string.create_haircut_commission,
                    description = Res.string.create_haircut_commission_description,
                    amount = viewState.commissionAmount,
                )
            }
            BcPrimaryButton(
                modifier = Modifier.fillMaxWidth().imePadding(),
                text = stringResource(Res.string.common_save),
                onClick = onSubmit
            )
        }
    }
}

@Composable
private fun CalculationItem(
    label: StringResource,
    description: StringResource,
    amount: String,
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = stringResource(label),
                style = MaterialTheme.typography.bodyLarge,
            )
            Text(
                text = amount,
                style = MaterialTheme.typography.titleMedium,
                color = Green700
            )
        }
        Text(
            text = stringResource(description),
            style = MaterialTheme.typography.labelMedium,
        )
    }
}

@Preview
@Composable
private fun CreateHaircutScreenPreview() {
    BarbckerTheme {
        CreateHaircutScreen(
            viewState = CreateHaircutViewState(
                barber = Barber(
                    uuid = "1",
                    name = "John",
                    commissionRate = 70,
                    phoneNumber = "123456789"
                ),
                price = "100",
                tip = "10",
                barbers = persistentListOf(),
                receivedAmount = "4 $",
                commissionAmount = "16 $",
            ),
            navigateBack = {},
            onBarberSelected = {},
            onPriceChanged = {},
            onTipChanged = {},
            onSubmit = {},
        )
    }
}