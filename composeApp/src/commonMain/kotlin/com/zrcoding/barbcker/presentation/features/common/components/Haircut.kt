package com.zrcoding.barbcker.presentation.features.common.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.common_tips
import barbcker.composeapp.generated.resources.common_total
import barbcker.composeapp.generated.resources.img_barber1
import com.zrcoding.barbcker.presentation.design_system.theme.Green700
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import com.zrcoding.barbcker.presentation.features.common.model.HaircutWithBarberUiModel
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource

@Composable
fun HaircutItem(haircut: HaircutWithBarberUiModel) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .border(
                width = 0.8.dp,
                color = MaterialTheme.colorScheme.surfaceContainer,
                shape = MaterialTheme.shapes.medium
            )
            .padding(MaterialTheme.dimension.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            modifier = Modifier.size(55.dp).clip(CircleShape),
            painter = painterResource(Res.drawable.img_barber1),
            contentDescription = "Barber avatar"
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
        ) {
            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
            ) {
                Text(
                    text = haircut.barberName,
                    style = MaterialTheme.typography.bodyLarge,
                )
                Text(
                    text = stringResource(Res.string.common_total, haircut.price),
                    style = MaterialTheme.typography.titleMedium,
                    color = Green700
                )
            }
            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
            ) {
                Text(
                    text = haircut.date.label(),
                    style = MaterialTheme.typography.labelMedium,
                )
                Text(
                    text = stringResource(Res.string.common_tips, haircut.tip),
                    style = MaterialTheme.typography.labelMedium,
                    color = Green700
                )
            }
        }
    }
}