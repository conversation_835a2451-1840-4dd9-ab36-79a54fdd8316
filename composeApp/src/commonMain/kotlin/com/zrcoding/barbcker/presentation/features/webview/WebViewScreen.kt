package com.zrcoding.barbcker.presentation.features.webview

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.multiplatform.webview.jsbridge.IJsMessageHandler
import com.multiplatform.webview.jsbridge.JsMessage
import com.multiplatform.webview.jsbridge.rememberWebViewJsBridge
import com.multiplatform.webview.web.WebView
import com.multiplatform.webview.web.WebViewNavigator
import com.multiplatform.webview.web.rememberWebViewState
import com.zrcoding.barbcker.presentation.design_system.components.BcTopAppBar
import io.github.aakira.napier.Napier

@Composable
fun WebViewScreen(
    url: String,
    title: String?,
    navigateBack: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Scaffold(
        modifier = modifier,
        topBar = {
            BcTopAppBar(
                title = title,
                onNavigationIconClicked = navigateBack
            )
        }
    ) {
        Box(modifier = Modifier.padding(it).fillMaxSize()) {
            val state = rememberWebViewState(url = url)
            val jsBridge = rememberWebViewJsBridge()
            WebView(
                modifier = Modifier.fillMaxSize(),
                state = state,
                captureBackPresses = false,
                webViewJsBridge = jsBridge
            )
            if (state.isLoading) {
                CircularProgressIndicator(modifier = Modifier.align(Alignment.Center))
            }
            LaunchedEffect(state.errorsForCurrentRequest) {
                state.errorsForCurrentRequest.forEach {
                    if (it.isFromMainFrame) {
                        Napier.e { "Error loading page: ${it.code} ${it.description}" }
                    }
                }
            }
            DisposableEffect(Unit) {
                state.webSettings.apply {
                    isJavaScriptEnabled = true
                    supportZoom = true
                    androidWebSettings.apply {
                        useWideViewPort = true
                        domStorageEnabled = true
                    }
                }
                jsBridge.register(handler = BackTopAppJsMessageHandler(onClick = navigateBack))
                onDispose { }
            }
        }
    }
}

class BackTopAppJsMessageHandler(val onClick: () -> Unit) : IJsMessageHandler {

    override fun handle(
        message: JsMessage,
        navigator: WebViewNavigator?,
        callback: (String) -> Unit
    ) {
        Napier.d { message.methodName + message.params }
        onClick()
    }

    override fun methodName(): String {
        return "Retour au Marchand"
    }
}