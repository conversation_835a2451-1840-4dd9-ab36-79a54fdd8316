package com.zrcoding.barbcker.presentation.features.common.model

import androidx.compose.runtime.Stable
import com.zrcoding.barbcker.domain.models.BarberRevenue
import com.zrcoding.barbcker.presentation.common.extension.round

@Stable
data class BarberRevenueUiModel(
    val barberId: String,
    val barberName: String,
    val totalHaircuts: Int,
    val totalRevenue: String,
    val totalTips: String,
)

fun BarberRevenue.toUiModel(currencySymbol: String): BarberRevenueUiModel {
    return BarberRevenueUiModel(
        barberId = barber.uuid,
        barberName = barber.name,
        totalHaircuts = totalHaircuts,
        totalRevenue = "${totalRevenue.round()} $currencySymbol",
        totalTips = "$totalTips $currencySymbol"
    )
}