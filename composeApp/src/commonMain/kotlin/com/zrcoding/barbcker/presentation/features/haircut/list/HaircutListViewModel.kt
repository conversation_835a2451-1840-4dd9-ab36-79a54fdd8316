package com.zrcoding.barbcker.presentation.features.haircut.list

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.navigation.toRoute
import app.cash.paging.map
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import com.zrcoding.barbcker.domain.repositories.HaircutRepository
import com.zrcoding.barbcker.presentation.features.common.model.toUiModel
import com.zrcoding.barbcker.presentation.navigation.HaircutList
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map

class HaircutListViewModel(
    private val haircutRepository: HaircutRepository,
    accountRepository: AccountRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val barberId: String? = savedStateHandle.toRoute<HaircutList>().barberId

    @OptIn(ExperimentalCoroutinesApi::class)
    val haircuts = accountRepository.getAccount().flatMapLatest { account ->
        if (barberId != null) {
            haircutRepository.observeBarberHaircuts(barberId)
        } else {
            haircutRepository.observeAll()
        }.map { pagingData ->
            pagingData.map {
                val currencySymbol =
                    ((account as? Account.Connected)?.currency ?: Currency.DOLLAR).symbol
                it.toUiModel(currencySymbol = currencySymbol)
            }
        }
    }
}