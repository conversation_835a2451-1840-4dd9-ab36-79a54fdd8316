package com.zrcoding.barbcker.presentation.features.home

import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilledIconButton
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBarDefaults
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.contentColorFor
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.bottom_bar_haircut
import com.zrcoding.barbcker.presentation.design_system.theme.White
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import com.zrcoding.barbcker.presentation.features.barber.list.BarbersListRoute
import com.zrcoding.barbcker.presentation.features.foryou.ForYouRoute
import com.zrcoding.barbcker.presentation.features.settings.SettingRoute
import com.zrcoding.barbcker.presentation.features.stats.StatsRoute
import com.zrcoding.barbcker.presentation.navigation.Barbers
import com.zrcoding.barbcker.presentation.navigation.ForYou
import com.zrcoding.barbcker.presentation.navigation.Settings
import com.zrcoding.barbcker.presentation.navigation.Stats
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.compose.viewmodel.koinViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    appState: AvitoAppState,
    navigateToUpsertBarber: (String?) -> Unit,
    navigateToHaircutList: (String?) -> Unit,
    navigateToPrivacyPolicy: () -> Unit,
    navigateToAboutUs: () -> Unit,
    navigateToAuth: () -> Unit,
    viewModel: HomeViewModel = koinViewModel()
) {
    val title = appState.currentTopLevelDestination?.titleTextId
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    title?.let {
                        Text(
                            modifier = Modifier.fillMaxWidth(),
                            text = stringResource(it),
                            style = MaterialTheme.typography.titleMedium,
                            textAlign = TextAlign.Center
                        )
                    }
                },
                expandedHeight = 45.dp,
                windowInsets = WindowInsets(0.dp),
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.background
                )
            )
        },
        bottomBar = {
            BcBottomBar(
                currentDestination = appState.currentDestination,
                onNavigateToDestination = appState::navigateToTopLevelDestination,
                onNavigateToCreateHaircut = appState::onNavigateToCreateHaircut
            )
        },
    ) {
        Column(modifier = Modifier.padding(it)) {
            HorizontalDivider(color = MaterialTheme.colorScheme.outlineVariant)
            Spacer(modifier = Modifier.height(MaterialTheme.dimension.large))
            NavHost(
                modifier = Modifier.weight(1f),
                navController = appState.homeNavController,
                startDestination = ForYou
            ) {
                composable<ForYou>(
                    enterTransition = { fadeIn() },
                    exitTransition = { fadeOut() }
                ) {
                    ForYouRoute(navigateToHaircutList = { navigateToHaircutList(null) })
                }
                composable<Barbers>(
                    enterTransition = { fadeIn() },
                    exitTransition = { fadeOut() }
                ) {
                    BarbersListRoute(navigateToUpsertBarber = navigateToUpsertBarber)
                }
                composable<Stats>(
                    enterTransition = { fadeIn() },
                    exitTransition = { fadeOut() }
                ) {
                    StatsRoute(navigateToHaircutList = navigateToHaircutList)
                }
                composable<Settings>(
                    enterTransition = { fadeIn() },
                    exitTransition = { fadeOut() }
                ) {
                    SettingRoute(
                        navigateToPrivacyPolicy = navigateToPrivacyPolicy,
                        navigateToAboutUs = navigateToAboutUs,
                        navigateToAuth = navigateToAuth
                    )
                }
            }
        }
    }
}

@Composable
private fun BcBottomBar(
    currentDestination: NavDestination?,
    onNavigateToDestination: (TopLevelDestination) -> Unit,
    onNavigateToCreateHaircut: () -> Unit,
    modifier: Modifier = Modifier
) {
    BottomNavigationBar(
        modifier = modifier,
        containerColor = MaterialTheme.colorScheme.background,
        onActionClick = onNavigateToCreateHaircut,
        windowInsets = WindowInsets(0.dp),
    ) {
        TopLevelDestination.entries.forEachIndexed { index, destination ->
            val selected = currentDestination.isRouteInHierarchy(destination.route)
            NavigationBarItem(
                selected = selected,
                icon = {
                    Icon(
                        painter = painterResource(destination.getIcon(selected)),
                        contentDescription = null
                    )
                },
                onClick = { onNavigateToDestination(destination) },
                colors = NavigationBarItemDefaults.colors(
                    selectedIconColor = MaterialTheme.colorScheme.primary,
                    indicatorColor = MaterialTheme.colorScheme.surfaceContainer,
                )
            )
            if (index == 1) {
                NavigationBarItem(selected = false, label = {}, icon = {}, onClick = {})
            }
        }
    }
}

@Composable
private fun BottomNavigationBar(
    modifier: Modifier = Modifier,
    containerColor: Color = NavigationBarDefaults.containerColor,
    contentColor: Color = MaterialTheme.colorScheme.contentColorFor(containerColor),
    windowInsets: WindowInsets = NavigationBarDefaults.windowInsets,
    onActionClick: () -> Unit,
    content: @Composable RowScope.() -> Unit,
) {
    Surface(
        color = containerColor,
        contentColor = contentColor,
        tonalElevation = 8.dp,
        shadowElevation = 8.dp,
        modifier = modifier
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .windowInsetsPadding(windowInsets)
                .defaultMinSize(minHeight = 84.dp),
            contentAlignment = Alignment.BottomCenter
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectableGroup(),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
                verticalAlignment = Alignment.CenterVertically,
                content = content
            )
            Column(
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(bottom = MaterialTheme.dimension.large),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                FilledIconButton(
                    modifier = Modifier.size(60.dp),
                    onClick = onActionClick,
                    colors = IconButtonDefaults.filledIconButtonColors(
                        containerColor = MaterialTheme.colorScheme.primary,
                        contentColor = White
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Open ad insert button icon"
                    )
                }
                Spacer(modifier = Modifier.height(MaterialTheme.dimension.small))
                Text(
                    text = stringResource(Res.string.bottom_bar_haircut),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onBackground
                )
            }
        }
    }
}