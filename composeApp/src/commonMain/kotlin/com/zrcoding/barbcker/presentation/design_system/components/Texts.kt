package com.zrcoding.barbcker.presentation.design_system.components

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withLink

@Composable
fun BcScreenTitle(
    text: String,
    modifier: Modifier = Modifier,
    textAlign: TextAlign = TextAlign.Center
) {
    Text(
        modifier = modifier,
        text = text,
        style = MaterialTheme.typography.headlineMedium,
        textAlign = textAlign,
        maxLines = 1,
        overflow = TextOverflow.Visible
    )
}

@Composable
fun BcScreenDescription(
    text: String,
    modifier: Modifier = Modifier,
    textAlign: TextAlign = TextAlign.Center
) {
    Text(
        modifier = modifier,
        text = text,
        style = MaterialTheme.typography.bodyLarge,
        textAlign = textAlign
    )
}

@Composable
fun BcTextWithClickablePart(
    text: String,
    clickablePart: String,
    onClick: () -> Unit,
) {
    val annotatedText = buildAnnotatedString {
        append(text)
        append(" ")
        withLink(
            link = LinkAnnotation.Clickable(
                tag = "",
                styles = TextLinkStyles(
                    style = SpanStyle(
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Medium,
                        textDecoration = TextDecoration.Underline
                    )
                ),
                linkInteractionListener = { onClick() }
            )
        ) {
            append(clickablePart)
        }
    }
    Text(
        text = annotatedText,
        style = MaterialTheme.typography.bodyMedium,
    )
}

