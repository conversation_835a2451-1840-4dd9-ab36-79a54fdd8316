package com.zrcoding.barbcker.presentation.features.auth.password_reset

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.sign_in_email_doesnt_exists
import barbcker.composeapp.generated.resources.sign_in_email_invalid
import barbcker.composeapp.generated.resources.sign_in_email_required
import barbcker.composeapp.generated.resources.sign_in_forgot_password_success
import com.zrcoding.barbcker.domain.models.ResetPasswordErrors
import com.zrcoding.barbcker.domain.models.Resource
import com.zrcoding.barbcker.domain.repositories.AuthRepository
import com.zrcoding.barbcker.presentation.common.extension.renderFailure
import com.zrcoding.barbcker.presentation.common.extension.renderSuccess
import com.zrcoding.barbcker.presentation.design_system.utils.UiText
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class ResetPasswordViewModel(
    private val authRepository: AuthRepository,
) : ViewModel() {

    private val _viewState = MutableStateFlow(ResetPasswordViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<ResetPasswordOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onEmailChanged(email: String) {
        _viewState.value = viewState.value.copy(email = email.trim(), emailError = null)
    }

    fun onSubmit() {
        val email = viewState.value.email.trim()
        if (email.isBlank()) {
            _viewState.value = viewState.value.copy(emailError = Res.string.sign_in_email_required)
            return
        }
        val emailRegex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$".toRegex()
        if (!email.matches(emailRegex)) {
            _viewState.value = viewState.value.copy(emailError = Res.string.sign_in_email_invalid)
            return
        }
        sendPasswordResetEmail(email)
    }

    private fun sendPasswordResetEmail(email: String) {
        viewModelScope.launch {
            _viewState.update { it.copy(isLoading = true) }
            val result = authRepository.sendPasswordResetEmail(email)
            _viewState.update { it.copy(isLoading = false) }
            when (result) {
                is Resource.Success -> {
                    renderSuccess(UiText.FromRes(Res.string.sign_in_forgot_password_success))
                    _oneTimeEvents.emit(ResetPasswordOneTimeEvents.NavigateToSignIn)
                }

                is Resource.Failure -> when (val failure = result.error) {
                    is ResetPasswordErrors.Network -> renderFailure(failure.error)
                    is ResetPasswordErrors.EmailDoesntExists -> renderFailure(
                        UiText.FromRes(Res.string.sign_in_email_doesnt_exists)
                    )
                }
            }
        }
    }
}