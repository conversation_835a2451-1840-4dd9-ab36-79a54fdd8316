package com.zrcoding.barbcker.presentation.features.complete_account

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.complete_account_shop_name_required
import com.zrcoding.barbcker.domain.models.CompleteAccountErrors
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.domain.models.Resource
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import com.zrcoding.barbcker.presentation.common.extension.renderFailure
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class CompleteAccountViewModel(
    private val accountRepository: AccountRepository,
) : ViewModel() {

    private val _viewState = MutableStateFlow(CompleteAccountViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<CompleteAccountOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onShopNameChanged(shopName: String) {
        _viewState.value = viewState.value.copy(shopName = shopName)
    }

    fun onCurrencyChanged(currency: Currency) {
        _viewState.value = viewState.value.copy(currency = currency)
    }

    fun onSubmit() {
        val (shopName, currency) = viewState.value
        if (shopName.isBlank()) {
            _viewState.update {
                it.copy(shopNameError = Res.string.complete_account_shop_name_required)
            }
            return
        }
        viewModelScope.launch {
            val result = accountRepository.updateAccount(shopName, currency)
            when (result) {
                is Resource.Success -> _oneTimeEvents.emit(CompleteAccountOneTimeEvents.NavigateToSetupBarbers)
                is Resource.Failure -> when (val failure = result.error) {
                    is CompleteAccountErrors.Network -> renderFailure(failure.error)
                    is CompleteAccountErrors.ShouldRelogIn -> _oneTimeEvents.emit(
                        CompleteAccountOneTimeEvents.NavigateToAuth
                    )
                }
            }
        }
    }
}