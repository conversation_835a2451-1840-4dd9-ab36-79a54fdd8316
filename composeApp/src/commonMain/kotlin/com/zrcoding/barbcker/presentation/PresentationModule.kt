package com.zrcoding.barbcker.presentation

import com.zrcoding.barbcker.presentation.features.app.AppViewModel
import com.zrcoding.barbcker.presentation.features.auth.authModule
import com.zrcoding.barbcker.presentation.features.barber.list.BarbersListViewModel
import com.zrcoding.barbcker.presentation.features.barber.setup.SetupBarbersViewModel
import com.zrcoding.barbcker.presentation.features.barber.upsert.UpsertBarberViewModel
import com.zrcoding.barbcker.presentation.features.complete_account.CompleteAccountViewModel
import com.zrcoding.barbcker.presentation.features.foryou.ForYouViewModel
import com.zrcoding.barbcker.presentation.features.haircut.ceate.CreateHaircutViewModel
import com.zrcoding.barbcker.presentation.features.haircut.list.HaircutListViewModel
import com.zrcoding.barbcker.presentation.features.home.HomeViewModel
import com.zrcoding.barbcker.presentation.features.onboarding.OnboardingViewModel
import com.zrcoding.barbcker.presentation.features.settings.SettingsViewModel
import com.zrcoding.barbcker.presentation.features.settings.settingsModule
import com.zrcoding.barbcker.presentation.features.stats.StatsViewModel
import org.koin.core.module.dsl.viewModelOf
import org.koin.dsl.module

val presentationModules = module {
    viewModelOf(::AppViewModel)
    viewModelOf(::OnboardingViewModel)
    viewModelOf(::CompleteAccountViewModel)
    viewModelOf(::SetupBarbersViewModel)
    viewModelOf(::UpsertBarberViewModel)
    viewModelOf(::CreateHaircutViewModel)
    viewModelOf(::HomeViewModel)
    viewModelOf(::ForYouViewModel)
    viewModelOf(::BarbersListViewModel)
    viewModelOf(::StatsViewModel)
    viewModelOf(::SettingsViewModel)
    viewModelOf(::HaircutListViewModel)
    includes(authModule, settingsModule)
}