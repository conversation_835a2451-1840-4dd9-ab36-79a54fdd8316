package com.zrcoding.barbcker.presentation.features.settings

import androidx.compose.runtime.Stable
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.ic_english
import barbcker.composeapp.generated.resources.ic_french
import barbcker.composeapp.generated.resources.settings_language_en
import barbcker.composeapp.generated.resources.settings_language_fr
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.StringResource
import org.koin.core.module.Module

@Stable
enum class Language(
    val code: String,
    val displayName: StringResource,
    val iconRes: DrawableResource
) {
    ENGLISH("en", Res.string.settings_language_en, Res.drawable.ic_english),
    FRENCH("fr", Res.string.settings_language_fr, Res.drawable.ic_french);

    companion object {
        fun fromCode(code: String): Language {
            return entries.firstOrNull { it.code == code } ?: ENGLISH
        }
    }
}

expect class Localization {
    fun changeLanguage(language: String)
    fun getCurrentLanguage(): String
}

expect val settingsModule: Module