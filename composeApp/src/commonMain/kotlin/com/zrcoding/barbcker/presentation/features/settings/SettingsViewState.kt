package com.zrcoding.barbcker.presentation.features.settings

import androidx.compose.runtime.Stable
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.settings_currency_dollar
import barbcker.composeapp.generated.resources.settings_currency_euro
import com.zrcoding.barbcker.domain.models.Currency
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.StringResource

@Stable
data class SettingsViewState(
    val shopName: String = "",
    val email: String = "",
    val currency: Currency = Currency.EURO,
    val language: Language = Language.ENGLISH,
    val isLoading: Boolean = false,
)

sealed interface SettingsOneTimeEvents {
    data object NavigateToAuth : SettingsOneTimeEvents
}

fun Currency.stringRes() = when (this) {
    Currency.DOLLAR -> Res.string.settings_currency_dollar
    Currency.EURO -> Res.string.settings_currency_euro
}

@Stable
data class SettingItem(
    val iconRes: DrawableResource,
    val nameRes: StringResource,
    val descriptionRes: String?,
    val onClick: () -> Unit,
)