package com.zrcoding.barbcker.presentation.features.haircut.ceate

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.create_haircut_barber_required
import barbcker.composeapp.generated.resources.create_haircut_price_required
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.domain.repositories.BarberRepository
import com.zrcoding.barbcker.domain.repositories.HaircutRepository
import com.zrcoding.barbcker.presentation.common.extension.round
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class CreateHaircutViewModel(
    private val account: Account.Connected,
    private val barberRepository: BarberRepository,
    private val haircutRepository: HaircutRepository,
) : ViewModel() {

    private val _viewState = MutableStateFlow(CreateHaircutViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvent = MutableSharedFlow<CreateHaircutOneTimeEvents>()
    val oneTimeEvent = _oneTimeEvent.asSharedFlow()

    init {
        viewModelScope.launch {
            val barbers = barberRepository.getAll().toPersistentList()
            if (barbers.size == 1) {
                _viewState.update {
                    it.copy(barber = barbers.first(), barbers = barbers)
                }
            } else {
                _viewState.update {
                    it.copy(barbers = barbers)
                }
            }
        }
    }

    fun onBarberSelected(barber: Barber) {
        _viewState.update { it.copy(barber = barber, barberError = null) }
        _viewState.update {
            it.copy(
                receivedAmount = shopOwnerAmount(it),
                commissionAmount = barberCommissionAmount(it)
            )
        }
    }

    fun onPriceChanged(price: String) {
        _viewState.update { it.copy(price = price, priceError = null) }
        _viewState.update {
            it.copy(
                receivedAmount = shopOwnerAmount(it),
                commissionAmount = barberCommissionAmount(it)
            )
        }
    }

    fun onTipChanged(tip: String) {
        _viewState.update { it.copy(tip = tip) }
        _viewState.update {
            it.copy(
                receivedAmount = shopOwnerAmount(it),
                commissionAmount = barberCommissionAmount(it)
            )
        }
    }

    fun onSubmit() {
        val (barber, price, tip) = _viewState.value
        if (barber == null) {
            _viewState.update {
                it.copy(barberError = Res.string.create_haircut_barber_required)
            }
            return
        }
        if (price.toDoubleOrNull() == null || price.toDoubleOrNull() == 0.0) {
            _viewState.update {
                it.copy(priceError = Res.string.create_haircut_price_required)
            }
            return
        }
        viewModelScope.launch {
            haircutRepository.insert(
                barber = barber,
                price = price.toDouble().round(),
                tip = tip.toDoubleOrNull()?.round() ?: 0.0
            )
            _oneTimeEvent.emit(CreateHaircutOneTimeEvents.Close)
        }
    }

    private fun shopOwnerAmount(viewState: CreateHaircutViewState): String {
        val barberCommissionRate = viewState.barber?.commissionRate ?: return ""
        val price = viewState.price.toDoubleOrNull() ?: return ""

        val amount = price * (1 - barberCommissionRate / 100.0)
        return amount.round().toString() + " " + account.currency.symbol
    }

    private fun barberCommissionAmount(viewState: CreateHaircutViewState): String {
        val barberCommissionRate = viewState.barber?.commissionRate ?: return ""
        val price = viewState.price.toDoubleOrNull() ?: return ""
        val amount = price * (barberCommissionRate / 100.0)
        val tip = viewState.tip.toDoubleOrNull()
        return buildString {
            append(amount.round())
            if (tip != null && tip > 0) {
                append(" + ")
                append(tip.round())
            }
            append(" ")
            append(account.currency.symbol)
        }
    }
}