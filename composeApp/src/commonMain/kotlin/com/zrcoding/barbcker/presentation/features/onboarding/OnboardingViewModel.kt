package com.zrcoding.barbcker.presentation.features.onboarding

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import com.zrcoding.barbcker.presentation.features.settings.Language
import com.zrcoding.barbcker.presentation.features.settings.Localization
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class OnboardingViewModel(
    private val accountRepository: AccountRepository,
    private val localization: Localization,
) : ViewModel() {

    private val _viewState = MutableStateFlow(
        OnboardingViewState(language = Language.fromCode(localization.getCurrentLanguage()))
    )
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<Unit>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onLanguageSelected(language: Language) {
        _viewState.value = viewState.value.copy(language = language)
        localization.changeLanguage(language.code)
    }

    fun onFinishClicked() {
        viewModelScope.launch {
            accountRepository.setSelectedLanguage()
            _oneTimeEvents.emit(Unit)
        }
    }
}