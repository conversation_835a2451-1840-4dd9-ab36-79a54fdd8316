package com.zrcoding.barbcker.presentation.features.common.model

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.common_at
import barbcker.composeapp.generated.resources.common_today
import barbcker.composeapp.generated.resources.common_yesterday
import barbcker.composeapp.generated.resources.days
import barbcker.composeapp.generated.resources.months
import com.zrcoding.barbcker.domain.models.HaircutWithBarber
import com.zrcoding.barbcker.presentation.common.extension.round
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.DayOfWeek
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.Month
import kotlinx.datetime.TimeZone
import kotlinx.datetime.minus
import kotlinx.datetime.toLocalDateTime
import org.jetbrains.compose.resources.stringArrayResource
import org.jetbrains.compose.resources.stringResource
import kotlin.time.Clock
import kotlin.time.ExperimentalTime

@Stable
data class HaircutWithBarberUiModel(
    val barberName: String,
    val price: String,
    val tip: String,
    val date: RelativeDate,
)

fun HaircutWithBarber.toUiModel(currencySymbol: String) = HaircutWithBarberUiModel(
    barberName = barber.name,
    price = "${haircut.price.round()} $currencySymbol",
    tip = "${haircut.tip.round()} $currencySymbol",
    date = getRelativeDayLabel(haircut.createAt)
)


/**
 * Returns a [RelativeDate] that represents a user-friendly, localized label
 * for a given [LocalDateTime]. The label is used to describe a date in a
 * concise and readable way for UI display.
 *
 * The returned [RelativeDate] can be one of the following:
 * - [RelativeDate.Today]: if the date is today
 * - [RelativeDate.Yesterday]: if the date is yesterday
 * - [RelativeDate.PastWeek]: if the date is within the last 7 days (excluding today and yesterday), includes the day of the week
 * - [RelativeDate.PastMonth]: if the date is older than a week, includes the numeric day and month
 *
 * @param date The [LocalDateTime] to evaluate.
 * @return [RelativeDate], which can be formatted for UI display using [RelativeDate.Label] in a @Composable scope.
 */
@OptIn(ExperimentalTime::class)
fun getRelativeDayLabel(date: LocalDateTime): RelativeDate {
    // Assumes system default timezone
    val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
    val target = date.date
    val hour = date.hour
    val minute = date.minute

    return when {
        target == now -> RelativeDate.Today(hour, minute)
        target == now.minus(1, DateTimeUnit.DAY) -> RelativeDate.Yesterday(hour, minute)
        target > now.minus(7, DateTimeUnit.DAY) -> RelativeDate.PastWeek(
            target.dayOfWeek,
            hour,
            minute
        )

        else -> RelativeDate.PastMonth(target.day, target.month, hour, minute)
    }
}

@Stable
sealed class RelativeDate(open val hour: Int, open val minute: Int) {

    @Composable
    fun label(): String {
        val time = "${stringResource(Res.string.common_at)} $hour:$minute"
        return when (this) {
            is Today -> "${stringResource(Res.string.common_today)} $time"
            is Yesterday -> "${stringResource(Res.string.common_yesterday)} $time"
            is PastWeek -> {
                val dayOfWeek = stringArrayResource(Res.array.days)[dayOfWeek.ordinal]
                    .lowercase().replaceFirstChar { it.uppercase() }
                "$dayOfWeek $time"
            }

            is PastMonth -> {
                val month = stringArrayResource(Res.array.months)[month.ordinal]
                    .lowercase().replaceFirstChar { it.uppercase() }
                "$dayOfMonth $month $time"
            }
        }
    }

    @Stable
    data class Today(override val hour: Int, override val minute: Int) : RelativeDate(hour, minute)

    @Stable
    data class Yesterday(
        override val hour: Int,
        override val minute: Int
    ) : RelativeDate(hour, minute)

    @Stable
    data class PastWeek(
        val dayOfWeek: DayOfWeek,
        override val hour: Int,
        override val minute: Int
    ) : RelativeDate(hour, minute)

    @Stable
    data class PastMonth(
        val dayOfMonth: Int,
        val month: Month,
        override val hour: Int,
        override val minute: Int
    ) : RelativeDate(hour, minute)
}
