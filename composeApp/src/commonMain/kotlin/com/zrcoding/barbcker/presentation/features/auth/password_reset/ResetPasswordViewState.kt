package com.zrcoding.barbcker.presentation.features.auth.password_reset

import androidx.compose.runtime.Stable
import org.jetbrains.compose.resources.StringResource

@Stable
data class ResetPasswordViewState(
    val email: String = "",
    val emailError: StringResource? = null,
    val isLoading: Boolean = false,
)

sealed interface ResetPasswordOneTimeEvents {
    data object NavigateToSignIn : ResetPasswordOneTimeEvents
}