package com.zrcoding.barbcker.presentation.features.auth.sign_in

import androidx.compose.runtime.Stable
import org.jetbrains.compose.resources.StringResource

@Stable
data class SignInViewState(
    val email: String = "",
    val password: String = "",
    val services: List<AuthService> = availableAuthServices,
    val emailError: StringResource? = null,
    val passwordError: StringResource? = null,
    val isLoading: Boolean = false,
)

enum class AuthService {
    GOOGLE,
    APPLE
}

expect val availableAuthServices: List<AuthService>

sealed interface SignInOneTimeEvents {
    data object NavigateToHome : SignInOneTimeEvents
    data object NavigateToCompleteAccount : SignInOneTimeEvents
    data object NavigateToSetupBarbers : SignInOneTimeEvents
}