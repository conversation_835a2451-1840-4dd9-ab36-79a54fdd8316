package com.zrcoding.barbcker.presentation.features.auth.sign_up

import androidx.compose.runtime.Stable
import org.jetbrains.compose.resources.StringResource

@Stable
data class SignUpViewState(
    val email: String = "",
    val password: String = "",
    val passwordConfirmation: String = "",
    val emailError: StringResource? = null,
    val passwordError: StringResource? = null,
    val passwordConfirmationError: StringResource? = null,
    val isLoading: Boolean = false,
)


sealed interface SignUpOneTimeEvents {
    data object NavigateToCompleteAccount : SignUpOneTimeEvents
}