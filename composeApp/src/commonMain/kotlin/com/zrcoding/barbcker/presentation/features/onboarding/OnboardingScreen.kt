package com.zrcoding.barbcker.presentation.features.onboarding

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.common_can_be_changed_later
import barbcker.composeapp.generated.resources.img_barber_chair
import barbcker.composeapp.generated.resources.onboarding_button
import barbcker.composeapp.generated.resources.onboarding_description
import barbcker.composeapp.generated.resources.onboarding_title
import barbcker.composeapp.generated.resources.settings_choose_language
import barbcker.composeapp.generated.resources.sign_in_green_text
import barbcker.composeapp.generated.resources.sign_in_red_text
import com.zrcoding.barbcker.analytics.TrackScreenViewEvent
import com.zrcoding.barbcker.analytics.models.AnalyticsEvent
import com.zrcoding.barbcker.presentation.design_system.components.BcPrimaryButton
import com.zrcoding.barbcker.presentation.design_system.components.BcScreenDescription
import com.zrcoding.barbcker.presentation.design_system.components.BcScreenTitle
import com.zrcoding.barbcker.presentation.design_system.theme.BarbckerTheme
import com.zrcoding.barbcker.presentation.design_system.theme.Green500
import com.zrcoding.barbcker.presentation.design_system.theme.Red500
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import com.zrcoding.barbcker.presentation.features.settings.Language
import kotlinx.coroutines.flow.collectLatest
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel


@Composable
fun OnboardingRoute(
    navigateToAuth: () -> Unit,
    viewModel: OnboardingViewModel = koinViewModel()
) {
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    OnboardingScreen(
        viewState = viewState,
        onLanguageSelected = viewModel::onLanguageSelected,
        onSubmit = viewModel::onFinishClicked
    )

    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collectLatest {
            navigateToAuth()
        }
    }
    TrackScreenViewEvent(screenName = AnalyticsEvent.ScreensNames.ONBOARDING)
}

@Composable
private fun OnboardingScreen(
    viewState: OnboardingViewState,
    onLanguageSelected: (Language) -> Unit,
    onSubmit: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(
                horizontal = MaterialTheme.dimension.screenPaddingHorizontal,
                vertical = MaterialTheme.dimension.big,
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.extraBig)
        ) {
            Image(
                modifier = Modifier.width(200.dp),
                painter = painterResource(Res.drawable.img_barber_chair),
                contentDescription = null,
                contentScale = ContentScale.Crop
            )
            Column(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                BcScreenTitle(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(Res.string.onboarding_title),
                    textAlign = TextAlign.Center
                )
                BcScreenDescription(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(Res.string.onboarding_description),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(MaterialTheme.dimension.default))
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            text = stringResource(Res.string.sign_in_red_text),
                            color = Red500,
                            style = MaterialTheme.typography.bodyLarge,
                            textAlign = TextAlign.Center
                        )
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = null,
                            tint = Red500
                        )
                    }
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            text = stringResource(Res.string.sign_in_green_text),
                            color = Green500,
                            style = MaterialTheme.typography.bodyLarge,
                            textAlign = TextAlign.Center
                        )
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = null,
                            tint = Green500
                        )
                    }
                }
            }
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
            ) {
                Text(
                    text = stringResource(resource = Res.string.settings_choose_language),
                    style = MaterialTheme.typography.titleSmall
                )
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.large)
                ) {
                    Language.entries.forEach {
                        val selected = viewState.language == it
                        Row(
                            modifier = Modifier
                                .weight(1f)
                                .clickable { onLanguageSelected(it) }
                                .clip(MaterialTheme.shapes.medium)
                                .border(
                                    width = if (selected) 2.dp else 0.8.dp,
                                    color = if (selected) {
                                        MaterialTheme.colorScheme.primary
                                    } else {
                                        MaterialTheme.colorScheme.outlineVariant
                                    },
                                    shape = MaterialTheme.shapes.medium
                                )
                                .padding(MaterialTheme.dimension.large),
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Image(
                                modifier = Modifier
                                    .width(30.dp)
                                    .height(24.dp)
                                    .clip(MaterialTheme.shapes.extraSmall),
                                painter = painterResource(it.iconRes),
                                contentDescription = null,
                                contentScale = ContentScale.Crop
                            )
                            Spacer(modifier = Modifier.width(MaterialTheme.dimension.medium))
                            Text(
                                text = stringResource(it.displayName),
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    }
                }
            }
        }
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(Res.string.common_can_be_changed_later),
                style = MaterialTheme.typography.labelMedium,
            )
            BcPrimaryButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(Res.string.onboarding_button),
                onClick = onSubmit,
            )
        }
    }
}

@Preview
@Composable
private fun OnboardingScreenPreview() {
    BarbckerTheme {
        Scaffold(
            modifier = Modifier.fillMaxSize()
        ) {
            OnboardingScreen(
                viewState = OnboardingViewState(),
                onLanguageSelected = {},
                onSubmit = {}
            )
        }
    }
}