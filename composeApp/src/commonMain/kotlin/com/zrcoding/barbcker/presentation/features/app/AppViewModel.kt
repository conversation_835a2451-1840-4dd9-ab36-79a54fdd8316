package com.zrcoding.barbcker.presentation.features.app

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import com.zrcoding.barbcker.domain.repositories.BarberRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module

class AppViewModel(
    private val accountRepository: AccountRepository,
    private val barberRepository: BarberRepository,
) : ViewModel() {

    private val _startDestination = MutableStateFlow<StartDestination>(StartDestination.Loading)
    val startDestination = _startDestination.asStateFlow()

    init {
        viewModelScope.launch {
            val isLanguageSelected = accountRepository.isLanguageSelected()
            _startDestination.value = if (!isLanguageSelected) {
                StartDestination.Ready.Onboarding
            } else {
                when (val account = accountRepository.getAccount().firstOrNull()) {
                    is Account.Connected -> {
                        if (account.shopName.isEmpty()) {
                            StartDestination.Ready.CompleteAccount
                        } else {
                            val barbers = barberRepository.getCount()
                            if (barbers == 0) {
                                StartDestination.Ready.SetupBarbers
                            } else StartDestination.Ready.Home
                        }
                    }

                    else -> StartDestination.Ready.Auth
                }
            }
        }
        viewModelScope.launch {
            accountRepository.getAccount().collectLatest { account ->
                if (account is Account.Connected) {
                    loadKoinModules(module { single<Account.Connected> { account } })
                }
            }
        }
    }
}