package com.zrcoding.barbcker.presentation.features.auth.sign_up

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withLink
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.common_continue
import barbcker.composeapp.generated.resources.sign_in_confirm_password_label
import barbcker.composeapp.generated.resources.sign_in_confirm_password_placeholder
import barbcker.composeapp.generated.resources.sign_in_email_label
import barbcker.composeapp.generated.resources.sign_in_email_placeholder
import barbcker.composeapp.generated.resources.sign_in_password_label
import barbcker.composeapp.generated.resources.sign_in_password_placeholder
import barbcker.composeapp.generated.resources.sign_in_privacy_policy_part1
import barbcker.composeapp.generated.resources.sign_in_privacy_policy_part2
import barbcker.composeapp.generated.resources.sign_up_description
import barbcker.composeapp.generated.resources.sign_up_title
import com.zrcoding.barbcker.analytics.TrackScreenViewEvent
import com.zrcoding.barbcker.analytics.models.AnalyticsEvent
import com.zrcoding.barbcker.presentation.design_system.components.BcPrimaryButton
import com.zrcoding.barbcker.presentation.design_system.components.BcScreenDescription
import com.zrcoding.barbcker.presentation.design_system.components.BcTextField
import com.zrcoding.barbcker.presentation.design_system.components.BcTopAppBar
import com.zrcoding.barbcker.presentation.design_system.theme.BarbckerTheme
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import kotlinx.coroutines.flow.collectLatest
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun SignUpRoute(
    navigateBack: () -> Unit,
    navigateToCompleteAccount: () -> Unit,
    navigateToPrivacyPolicy: () -> Unit,
    viewModel: SignUpViewModel = koinViewModel()
) {
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    Column(modifier = Modifier.fillMaxSize()) {
        BcTopAppBar(
            onNavigationIconClicked = navigateBack,
            title = stringResource(Res.string.sign_up_title)
        )
        SignUpScreen(
            viewState = viewState,
            onEmailChanged = viewModel::onEmailChanged,
            onPasswordChanged = viewModel::onPasswordChanged,
            onPasswordConfirmationChanged = viewModel::onPasswordConfirmationChanged,
            onSubmitClicked = viewModel::onSubmit,
            onPrivacyPolicyClicked = navigateToPrivacyPolicy,
            modifier = Modifier.fillMaxSize()
        )
    }
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collectLatest { event ->
            when (event) {
                SignUpOneTimeEvents.NavigateToCompleteAccount -> navigateToCompleteAccount()
            }
        }
    }
    TrackScreenViewEvent(screenName = AnalyticsEvent.ScreensNames.SIGN_UP)
}

@Composable
private fun SignUpScreen(
    viewState: SignUpViewState,
    onEmailChanged: (String) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onPasswordConfirmationChanged: (String) -> Unit,
    onSubmitClicked: () -> Unit,
    onPrivacyPolicyClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(
                horizontal = MaterialTheme.dimension.screenPaddingHorizontal,
                vertical = MaterialTheme.dimension.big,
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.extraBig)
        ) {
            BcScreenDescription(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(Res.string.sign_up_description),
                textAlign = TextAlign.Start
            )
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                val focusManager = LocalFocusManager.current
                BcTextField(
                    modifier = Modifier.fillMaxWidth(),
                    value = viewState.email,
                    onValueChanged = onEmailChanged,
                    title = Res.string.sign_in_email_label,
                    placeholder = Res.string.sign_in_email_placeholder,
                    error = viewState.emailError?.let { stringResource(it) },
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Email,
                        imeAction = ImeAction.Next,
                    ),
                    singleLine = true
                )
                var passwordVisibility by remember { mutableStateOf(false) }
                BcTextField(
                    modifier = Modifier.fillMaxWidth(),
                    value = viewState.password,
                    onValueChanged = onPasswordChanged,
                    title = Res.string.sign_in_password_label,
                    placeholder = Res.string.sign_in_password_placeholder,
                    error = viewState.passwordError?.let { stringResource(it) },
                    trailingIcon = {
                        IconButton(onClick = { passwordVisibility = !passwordVisibility }) {
                            Icon(
                                imageVector = if (passwordVisibility) {
                                    Icons.Default.Visibility
                                } else Icons.Default.VisibilityOff,
                                contentDescription = null
                            )
                        }
                    },
                    visualTransformation = if (passwordVisibility) {
                        VisualTransformation.None
                    } else PasswordVisualTransformation(),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Password,
                        imeAction = ImeAction.Next,
                    ),
                    singleLine = true
                )
                var passwordConfirmationVisibility by remember { mutableStateOf(false) }
                BcTextField(
                    modifier = Modifier.fillMaxWidth(),
                    value = viewState.passwordConfirmation,
                    onValueChanged = onPasswordConfirmationChanged,
                    title = Res.string.sign_in_confirm_password_label,
                    placeholder = Res.string.sign_in_confirm_password_placeholder,
                    error = viewState.passwordConfirmationError?.let { stringResource(it) },
                    trailingIcon = {
                        IconButton(onClick = {
                            passwordConfirmationVisibility = !passwordConfirmationVisibility
                        }) {
                            Icon(
                                imageVector = if (passwordConfirmationVisibility) {
                                    Icons.Default.Visibility
                                } else Icons.Default.VisibilityOff,
                                contentDescription = null
                            )
                        }
                    },
                    visualTransformation = if (passwordConfirmationVisibility) {
                        VisualTransformation.None
                    } else PasswordVisualTransformation(),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Password,
                        imeAction = ImeAction.Done,
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            focusManager.clearFocus()
                            onSubmitClicked()
                        }
                    ),
                    singleLine = true
                )
                BcPrimaryButton(
                    modifier = Modifier.width(250.dp),
                    text = stringResource(Res.string.common_continue),
                    loading = viewState.isLoading,
                    onClick = {
                        focusManager.clearFocus()
                        onSubmitClicked()
                    }
                )
            }
        }
        Text(
            text = buildAnnotatedString {
                append(stringResource(Res.string.sign_in_privacy_policy_part1))
                withLink(
                    link = LinkAnnotation.Clickable(
                        tag = "privacy_policy",
                        styles = TextLinkStyles(
                            style = SpanStyle(
                                fontWeight = MaterialTheme.typography.bodySmall.fontWeight,
                                fontSize = MaterialTheme.typography.bodySmall.fontSize,
                                textDecoration = TextDecoration.Underline
                            )
                        ),
                        linkInteractionListener = {
                            onPrivacyPolicyClicked()
                        }
                    )
                ) {
                    append(stringResource(Res.string.sign_in_privacy_policy_part2))
                }
            },
            style = MaterialTheme.typography.bodySmall,
            textAlign = TextAlign.Center
        )
    }
}

@Preview
@Composable
fun SignInPreview() {
    BarbckerTheme {
        Box(
            modifier = Modifier.fillMaxSize().background(MaterialTheme.colorScheme.background)
        ) {
            SignUpScreen(
                viewState = SignUpViewState(),
                onEmailChanged = {},
                onPasswordChanged = {},
                onPasswordConfirmationChanged = {},
                onSubmitClicked = {},
                onPrivacyPolicyClicked = {}
            )
        }
    }
}