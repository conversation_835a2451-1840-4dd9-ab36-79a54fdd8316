package com.zrcoding.barbcker.presentation.features.barber.list

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PersonAddAlt1
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import app.cash.paging.compose.LazyPagingItems
import app.cash.paging.compose.collectAsLazyPagingItems
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.setup_barbers_add_barber_button
import barbcker.composeapp.generated.resources.setup_barbers_delete_barber_confirmation_description
import barbcker.composeapp.generated.resources.setup_barbers_delete_barber_confirmation_title
import com.zrcoding.barbcker.analytics.TrackScreenViewEvent
import com.zrcoding.barbcker.analytics.models.AnalyticsEvent
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.presentation.design_system.components.BcConfirmationAlertDialog
import com.zrcoding.barbcker.presentation.design_system.components.BcPrimaryButton
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import com.zrcoding.barbcker.presentation.features.barber.common.BarbersList
import org.jetbrains.compose.resources.stringResource
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun BarbersListRoute(
    navigateToUpsertBarber: (String?) -> Unit,
    viewModel: BarbersListViewModel = koinViewModel()
) {
    val barbers = viewModel.barbers.collectAsLazyPagingItems()
    var barberToDelete by remember { mutableStateOf<Barber?>(null) }
    BarbersListScreen(
        barbers = barbers,
        onAddBarberClicked = { navigateToUpsertBarber(null) },
        onBarberClicked = { navigateToUpsertBarber(it.uuid) },
        onDeleteBarber = { barberToDelete = it },
    )
    if (barberToDelete != null) {
        BcConfirmationAlertDialog(
            title = stringResource(Res.string.setup_barbers_delete_barber_confirmation_title),
            description = stringResource(Res.string.setup_barbers_delete_barber_confirmation_description),
            onConfirm = {
                viewModel.onDeleteBarber(barberToDelete!!)
                barberToDelete = null
            },
            onCancel = {
                barberToDelete = null
            }
        )
    }
    TrackScreenViewEvent(screenName = AnalyticsEvent.ScreensNames.BARBERS_LIST)
}

@Composable
private fun BarbersListScreen(
    barbers: LazyPagingItems<Barber>,
    onAddBarberClicked: () -> Unit,
    onBarberClicked: (Barber) -> Unit,
    onDeleteBarber: (Barber) -> Unit,
) {
    Box(modifier = Modifier.fillMaxSize()) {
        BarbersList(
            lazyPagingItems = barbers,
            onBarberClicked = onBarberClicked,
            onDeleteBarber = onDeleteBarber,
        )
        BcPrimaryButton(
            modifier = Modifier.align(Alignment.BottomEnd).padding(
                end = MaterialTheme.dimension.screenPaddingHorizontal,
                bottom = MaterialTheme.dimension.screenPaddingHorizontal
            ),
            text = stringResource(Res.string.setup_barbers_add_barber_button),
            leadingIcon = {
                Icon(
                    modifier = Modifier.size(MaterialTheme.dimension.bigger),
                    imageVector = Icons.Filled.PersonAddAlt1,
                    contentDescription = null
                )
            },
            onClick = onAddBarberClicked,
        )
    }
}