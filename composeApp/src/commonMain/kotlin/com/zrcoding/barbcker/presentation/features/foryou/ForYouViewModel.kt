package com.zrcoding.barbcker.presentation.features.foryou

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import com.zrcoding.barbcker.domain.repositories.HaircutRepository
import com.zrcoding.barbcker.domain.repositories.StatsRepository
import com.zrcoding.barbcker.domain.utils.DateUtils
import com.zrcoding.barbcker.presentation.common.extension.round
import com.zrcoding.barbcker.presentation.features.common.model.toUiModel
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class ForYouViewModel(
    private val statsRepository: StatsRepository,
    private val accountRepository: AccountRepository,
    private val haircutRepository: HaircutRepository,
) : ViewModel() {

    @OptIn(ExperimentalCoroutinesApi::class)
    private val _viewState = MutableStateFlow(ForYouViewState())
    val viewState = _viewState.asStateFlow()

    init {
        viewModelScope.launch {
            combine(
                flow = accountRepository.getAccount(),
                flow2 = statsRepository.getTotalRevenueForPeriod(
                    startTimestamp = DateUtils.getTodayRange().first,
                    endTimestamp = DateUtils.getTodayRange().second
                ),
                flow3 = statsRepository.getShopOwnerTotalRevenueForPeriod(
                    startTimestamp = DateUtils.getTodayRange().first,
                    endTimestamp = DateUtils.getTodayRange().second
                ),
                flow4 = statsRepository.getTotalTipsForPeriod(
                    startTimestamp = DateUtils.getTodayRange().first,
                    endTimestamp = DateUtils.getTodayRange().second
                ),
                flow5 = haircutRepository.getLatestHaircuts(limit = 5),
                transform = { account, totalRevenue, shopOwnerTotalRevenue, totalTips, latestHaircuts ->
                    // TODO Call logout if account is not connected
                    val currency =
                        ((account as? Account.Connected)?.currency ?: Currency.DOLLAR).symbol
                    _viewState.value.copy(
                        totalRevenue = "${totalRevenue.round()} $currency",
                        shopOwnerTotalRevenue = "${shopOwnerTotalRevenue.round()} $currency",
                        totalTips = "${totalTips.round()} $currency",
                        latestHaircuts = latestHaircuts
                            .map { it.toUiModel(currency) }
                            .toPersistentList(),
                        isLoading = false,
                    )
                }
            ).collectLatest { state -> _viewState.update { state } }
        }
    }
}