package com.zrcoding.barbcker.presentation.features.foryou

import androidx.compose.runtime.Stable
import com.zrcoding.barbcker.presentation.features.common.model.HaircutWithBarberUiModel
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf

@Stable
data class ForYouViewState(
    val totalRevenue: String = "0.0",
    val shopOwnerTotalRevenue: String = "0.0",
    val totalTips: String = "0.0",
    val latestHaircuts: PersistentList<HaircutWithBarberUiModel> = persistentListOf(),
    val isLoading: Boolean = true,
)
