package com.zrcoding.barbcker.presentation.features.foryou

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.for_you_latest_haircuts
import barbcker.composeapp.generated.resources.for_you_no_haircuts
import barbcker.composeapp.generated.resources.for_you_no_haircuts_description
import barbcker.composeapp.generated.resources.for_you_see_history
import barbcker.composeapp.generated.resources.for_you_total_Tips
import barbcker.composeapp.generated.resources.for_you_total_revenue
import barbcker.composeapp.generated.resources.for_you_total_revenue_after_commissions
import barbcker.composeapp.generated.resources.ic_arrow_down
import barbcker.composeapp.generated.resources.img_hourglass
import com.zrcoding.barbcker.analytics.TrackScreenViewEvent
import com.zrcoding.barbcker.analytics.models.AnalyticsEvent
import com.zrcoding.barbcker.presentation.design_system.components.BcPrimaryButton
import com.zrcoding.barbcker.presentation.design_system.theme.BarbckerTheme
import com.zrcoding.barbcker.presentation.design_system.theme.Green500
import com.zrcoding.barbcker.presentation.design_system.theme.White
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import com.zrcoding.barbcker.presentation.features.common.components.HaircutItem
import kotlinx.collections.immutable.persistentListOf
import org.jetbrains.compose.resources.StringResource
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin

@Composable
fun ForYouRoute(
    navigateToHaircutList: () -> Unit,
    viewModel: ForYouViewModel = koinViewModel()
) {
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    ForYouScreen(viewState = viewState, navigateToHaircutList = navigateToHaircutList)
    TrackScreenViewEvent(screenName = AnalyticsEvent.ScreensNames.FOR_YOU)
}

@Composable
private fun ForYouScreen(
    viewState: ForYouViewState,
    navigateToHaircutList: () -> Unit,
) {
    Box(modifier = Modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.bigger),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            ForYouScreenAmounts(
                total = viewState.totalRevenue,
                totalAfterCommissions = viewState.shopOwnerTotalRevenue,
                totalTips = viewState.totalTips,
            )
            when {
                viewState.isLoading -> Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }

                viewState.latestHaircuts.isEmpty() -> ForYouScreenEmptyState()

                else -> Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
                ) {
                    Text(
                        text = stringResource(Res.string.for_you_latest_haircuts),
                        style = MaterialTheme.typography.titleMedium
                    )
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
                    ) {
                        viewState.latestHaircuts.forEach { haircut ->
                            HaircutItem(haircut = haircut)
                        }
                    }
                }
            }
            if (viewState.latestHaircuts.isEmpty().not()) {
                BcPrimaryButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = MaterialTheme.dimension.extraBig),
                    text = stringResource(Res.string.for_you_see_history),
                    onClick = navigateToHaircutList,
                )
            }
        }
        if (viewState.isLoading.not() && viewState.latestHaircuts.isEmpty()) {
            Icon(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = MaterialTheme.dimension.medium),
                painter = painterResource(Res.drawable.ic_arrow_down),
                contentDescription = null,
            )
        }
    }
}

@Composable
private fun ForYouScreenEmptyState() {
    Column(
        modifier = Modifier.fillMaxWidth().padding(top = MaterialTheme.dimension.extraBig),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(
            modifier = Modifier.width(120.dp),
            painter = painterResource(Res.drawable.img_hourglass),
            contentDescription = null,
            contentScale = ContentScale.Fit
        )
        Text(
            text = stringResource(Res.string.for_you_no_haircuts),
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center
        )
        Text(
            text = stringResource(Res.string.for_you_no_haircuts_description),
            style = MaterialTheme.typography.labelMedium,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun ForYouScreenAmounts(
    total: String,
    totalAfterCommissions: String,
    totalTips: String,
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clip(MaterialTheme.shapes.large),
        colors = CardDefaults.cardColors(containerColor = Color.Yellow)
    ) {
        // Infinite rotation animation
        val infiniteTransition = rememberInfiniteTransition()
        val rotation by infiniteTransition.animateFloat(
            initialValue = 0f,
            targetValue = 360f,
            animationSpec = infiniteRepeatable(
                animation = tween(10000, easing = LinearEasing),
                repeatMode = RepeatMode.Restart
            )
        )
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .drawBehind {
                    val stripeHeight = 20.dp.toPx()
                    val tiltAngle = 10f
                    val tiltRad = tiltAngle * (PI / 180.0)
                    val centerX = size.width / 2
                    val radius = size.width / 3
                    val colors = listOf(
                        Color(0xFFD32F2F), // Red
                        Color(0xFF2905A1), // Blue
                        Color.White        // White
                    )

                    val stripeCountY = (size.height / stripeHeight).toInt() + 2
                    val stripeCountX = (size.width / stripeHeight).toInt() + 2

                    // Draw horizontal stripes across the cylinder
                    for (j in 0 until stripeCountY) {
                        val y = j * stripeHeight
                        val color = colors[j % colors.size]

                        for (i in 0 until stripeCountX) {
                            // Cylindrical rotation effect
                            val angle = (i * stripeHeight / radius) + (rotation * (PI / 180.0))
                            val x = centerX + radius * sin(angle).toFloat()
                            val widthScale = 0.5f + 0.5f * cos(angle).toFloat()

                            // Calculate start and end points of the tilted horizontal line
                            val dx = stripeHeight * cos(tiltRad).toFloat() * widthScale
                            val dy = stripeHeight * sin(tiltRad).toFloat() * widthScale

                            drawLine(
                                color = color,
                                start = Offset(x - dx / 2, y - dy / 2),
                                end = Offset(x + dx / 2, y + dy / 2),
                                strokeWidth = stripeHeight
                            )
                        }
                    }
                }.padding(MaterialTheme.dimension.large)
        ) {
            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
            ) {
                AmountStat(
                    label = Res.string.for_you_total_revenue_after_commissions,
                    amount = totalAfterCommissions,
                    modifier = Modifier.fillMaxWidth(),
                )
                listOf(
                    total to Res.string.for_you_total_revenue,
                    totalTips to Res.string.for_you_total_Tips,
                ).forEach {
                    AmountStat(
                        label = it.second,
                        amount = it.first,
                    )
                }
            }
        }
    }
}

@Composable
private fun AmountStat(
    label: StringResource,
    amount: String,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
    ) {
        Text(
            text = stringResource(label),
            style = MaterialTheme.typography.labelLarge,
            color = White
        )
        Text(
            text = amount,
            style = MaterialTheme.typography.headlineLarge,
            color = Green500
        )
    }
}

@Preview
@Composable
private fun ForYouScreenPreview() {
    BarbckerTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
        ) {
            ForYouScreen(
                viewState = ForYouViewState(
                    totalRevenue = "150.0 $",
                    shopOwnerTotalRevenue = "50.0 $",
                    totalTips = "150.0 $",
                    latestHaircuts = persistentListOf(),
                    isLoading = false,
                ),
                navigateToHaircutList = {}
            )
        }
    }
}