package com.zrcoding.barbcker.presentation.design_system.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.window.DialogProperties
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.common_no
import barbcker.composeapp.generated.resources.common_yes
import com.zrcoding.barbcker.presentation.design_system.theme.Blue300
import com.zrcoding.barbcker.presentation.design_system.theme.Blue500
import com.zrcoding.barbcker.presentation.design_system.theme.Blue800
import com.zrcoding.barbcker.presentation.design_system.theme.Brown100
import com.zrcoding.barbcker.presentation.design_system.theme.Brown400
import com.zrcoding.barbcker.presentation.design_system.theme.Brown800
import com.zrcoding.barbcker.presentation.design_system.theme.Red200
import com.zrcoding.barbcker.presentation.design_system.theme.Red500
import com.zrcoding.barbcker.presentation.design_system.theme.Red700
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview

@Composable
fun BcConfirmationAlertDialog(
    modifier: Modifier = Modifier,
    title: String,
    description: String,
    onConfirm: () -> Unit,
    onCancel: () -> Unit,
    cancelText: String = stringResource(Res.string.common_no),
    confirmText: String = stringResource(Res.string.common_yes),
    properties: DialogProperties = DialogProperties()
) {
    AlertDialog(
        modifier = modifier,
        title = {
            Text(text = title, style = MaterialTheme.typography.titleMedium)
        },
        text = {
            Text(text = description, style = MaterialTheme.typography.bodyMedium)
        },
        confirmButton = {
            BcPrimaryButton(
                text = confirmText,
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error,
                    contentColor = MaterialTheme.colorScheme.onError
                )
            )
        },
        dismissButton = {
            BcSecondaryButton(
                modifier = Modifier.padding(horizontal = MaterialTheme.dimension.small),
                text = cancelText,
                onClick = onCancel,
            )
        },
        onDismissRequest = onCancel,
        shape = MaterialTheme.shapes.large,
        containerColor = MaterialTheme.colorScheme.surfaceContainerLowest,
        titleContentColor = MaterialTheme.colorScheme.onBackground,
        textContentColor = MaterialTheme.colorScheme.onBackground,
        properties = properties
    )
}

enum class AvAlertType(
    val bgColor: Color,
    val textColor: Color,
    val icon: ImageVector,
    val iconTint: Color,
) {
    Info(bgColor = Blue300, textColor = Blue800, Icons.Default.Info, iconTint = Blue500),
    Warning(bgColor = Brown100, textColor = Brown800, Icons.Default.Warning, iconTint = Brown400),
    Error(bgColor = Red200, textColor = Red700, Icons.Default.Info, iconTint = Red500),
}


@Composable
fun BcAlert(
    title: String,
    description: String,
    type: AvAlertType,
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .background(type.bgColor, MaterialTheme.shapes.large)
            .padding(MaterialTheme.dimension.large)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
            ) {
                Icon(
                    imageVector = type.icon,
                    contentDescription = null,
                    tint = type.iconTint
                )
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = type.textColor
                )
            }
        }
        Text(
            text = description,
            style = MaterialTheme.typography.bodyMedium,
            color = type.textColor,
        )
    }
}

@Preview
@Composable
private fun BcAlertPreview() {
    BcAlert(
        title = "Tips",
        description = "Commission rate  = 30% means that the barber  you’re creating gets 30% of each haircuts,  and the shop owner gets 70%.\u2028If you’re a solo barber create a barber with the commission rate = 100%.",
        type = AvAlertType.Info,
        onDismissRequest = { }
    )
}
