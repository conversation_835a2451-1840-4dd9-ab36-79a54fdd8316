package com.zrcoding.barbcker.presentation.features.stats

import androidx.compose.runtime.Stable
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.format
import kotlinx.datetime.format.MonthNames
import kotlinx.datetime.format.char
import kotlinx.datetime.toLocalDateTime
import kotlin.time.Clock
import kotlin.time.ExperimentalTime
import kotlin.time.Instant

@Stable
data class StatsViewState @OptIn(ExperimentalTime::class) constructor(
    val monthsFilter: List<MonthFilter> = emptyList(),
    val selectedMonth: MonthFilter? = null,
    val selectedStartDateMillis: Long = Clock.System.now().toEpochMilliseconds(),
    val selectedEndDateMillis: Long = Clock.System.now().toEpochMilliseconds(),
    val totalRevenue: String = "0.0",
    val shopOwnerTotalRevenue: String = "0.0",
    val totalTips: String = "0.0",
)

data class MonthFilter(
    val localDateTime: LocalDate,
    val label: String
)

@OptIn(ExperimentalTime::class)
fun formatToDisplayedDate(epochMillis: Long): String {
    return Instant.fromEpochMilliseconds(epochMillis)
        .toLocalDateTime(TimeZone.UTC)
        .format(
            LocalDateTime.Format {
                day()
                char(' ')
                monthName(MonthNames.ENGLISH_ABBREVIATED)
                char(' ')
                year()
            }
        )
}
