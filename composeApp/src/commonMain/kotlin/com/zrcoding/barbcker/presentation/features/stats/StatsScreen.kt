package com.zrcoding.barbcker.presentation.features.stats

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CalendarMonth
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.DateRangePicker
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshDefaults
import androidx.compose.material3.pulltorefresh.PullToRefreshState
import androidx.compose.material3.pulltorefresh.pullToRefresh
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.material3.rememberDateRangePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import app.cash.paging.PagingData
import app.cash.paging.compose.LazyPagingItems
import app.cash.paging.compose.collectAsLazyPagingItems
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.common_cancel
import barbcker.composeapp.generated.resources.common_haircuts
import barbcker.composeapp.generated.resources.common_ok
import barbcker.composeapp.generated.resources.common_tips
import barbcker.composeapp.generated.resources.common_total
import barbcker.composeapp.generated.resources.for_you_barbers_revenue
import barbcker.composeapp.generated.resources.for_you_total_Tips
import barbcker.composeapp.generated.resources.for_you_total_revenue
import barbcker.composeapp.generated.resources.for_you_total_revenue_after_commissions
import barbcker.composeapp.generated.resources.img_barber1
import com.zrcoding.barbcker.analytics.TrackScreenViewEvent
import com.zrcoding.barbcker.analytics.models.AnalyticsEvent
import com.zrcoding.barbcker.presentation.common.extension.isEmpty
import com.zrcoding.barbcker.presentation.common.extension.isLoading
import com.zrcoding.barbcker.presentation.design_system.components.BcPrimaryButton
import com.zrcoding.barbcker.presentation.design_system.components.BcSecondaryButton
import com.zrcoding.barbcker.presentation.design_system.theme.BarbckerTheme
import com.zrcoding.barbcker.presentation.design_system.theme.Green700
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import com.zrcoding.barbcker.presentation.features.common.model.BarberRevenueUiModel
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.flowOf
import org.jetbrains.compose.resources.StringResource
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel
import kotlin.time.ExperimentalTime

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StatsRoute(
    navigateToHaircutList: (String) -> Unit,
    viewModel: StatsViewModel = koinViewModel()
) {
    val barbersRevenue = viewModel.barbersRevenue.collectAsLazyPagingItems()
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    val refreshState: PullToRefreshState = rememberPullToRefreshState()
    StatsScreen(
        barbersRevenue = barbersRevenue,
        viewState = viewState,
        refreshState = refreshState,
        onMonthFilterChanged = viewModel::onMonthFilterChanged,
        onDateRangeChanged = viewModel::onDateRangeChanged,
        onBarberClicked = navigateToHaircutList,
    )
    TrackScreenViewEvent(screenName = AnalyticsEvent.ScreensNames.STATS)
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun StatsScreen(
    barbersRevenue: LazyPagingItems<BarberRevenueUiModel>,
    viewState: StatsViewState,
    refreshState: PullToRefreshState,
    onMonthFilterChanged: (MonthFilter) -> Unit,
    onDateRangeChanged: (Long?, Long?) -> Unit,
    onBarberClicked: (String) -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .pullToRefresh(
                isRefreshing = barbersRevenue.isLoading(),
                state = refreshState,
                enabled = false,
                onRefresh = {}
            )
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.bigger)
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
            ) {
                LazyRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
                ) {
                    items(viewState.monthsFilter) { monthFilter ->
                        MonthFilterItem(
                            label = monthFilter.label,
                            isSelected = monthFilter == viewState.selectedMonth,
                            onClick = { onMonthFilterChanged(monthFilter) }
                        )
                    }
                }
                DateRangeFilter(
                    startDate = viewState.selectedStartDateMillis,
                    endDate = viewState.selectedEndDateMillis,
                    onDateRangeChanged = onDateRangeChanged
                )
            }
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
            ) {
                AmountStat(
                    modifier = Modifier.fillMaxWidth(),
                    title = Res.string.for_you_total_revenue_after_commissions,
                    amount = viewState.shopOwnerTotalRevenue,
                )
                FlowRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
                ) {
                    AmountStat(
                        modifier = Modifier.weight(1f),
                        title = Res.string.for_you_total_revenue,
                        amount = viewState.totalRevenue,
                    )
                    AmountStat(
                        modifier = Modifier.weight(1f),
                        title = Res.string.for_you_total_Tips,
                        amount = viewState.totalTips,
                    )
                }
            }
            BarbersRevenue(
                modifier = Modifier.padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal),
                barbersRevenue = barbersRevenue,
                onBarberClicked = onBarberClicked,
            )
        }
        PullToRefreshDefaults.Indicator(
            modifier = Modifier.align(Alignment.TopCenter),
            isRefreshing = barbersRevenue.isLoading(),
            state = refreshState,
        )
    }
}

@Composable
private fun MonthFilterItem(label: String, isSelected: Boolean, onClick: () -> Unit) {
    FilterChip(
        selected = isSelected,
        onClick = onClick,
        label = { Text(text = label) },
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = MaterialTheme.colorScheme.primary,
            selectedLabelColor = MaterialTheme.colorScheme.onPrimary,
            containerColor = MaterialTheme.colorScheme.surfaceContainer,
            labelColor = MaterialTheme.colorScheme.onBackground,
        ),
        border = null
    )
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalTime::class)
@Composable
private fun DateRangeFilter(
    startDate: Long,
    endDate: Long,
    onDateRangeChanged: (Long?, Long?) -> Unit,
) {
    var showDateRangePicker by remember { mutableStateOf(false) }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal)
            .clip(MaterialTheme.shapes.medium)
            .clickable { showDateRangePicker = true }
            .border(
                width = 0.8.dp,
                color = MaterialTheme.colorScheme.surfaceContainer,
                shape = MaterialTheme.shapes.medium
            )
            .padding(
                horizontal = MaterialTheme.dimension.medium,
                vertical = MaterialTheme.dimension.small
            ),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
        ) {
            Text(
                text = formatToDisplayedDate(startDate),
                style = MaterialTheme.typography.bodyLarge
            )
            Text(text = " - ", style = MaterialTheme.typography.bodyLarge)
            Text(
                text = formatToDisplayedDate(endDate),
                style = MaterialTheme.typography.bodyLarge
            )
        }
        IconButton(onClick = { showDateRangePicker = true }) {
            Icon(
                imageVector = Icons.Default.CalendarMonth,
                contentDescription = null,
            )
        }
    }
    if (showDateRangePicker) {
        val dateRangePickerState = rememberDateRangePickerState(
            initialSelectedStartDateMillis = startDate,
            initialSelectedEndDateMillis = endDate,
        )
        DatePickerDialog(
            onDismissRequest = { showDateRangePicker = false },
            shape = MaterialTheme.shapes.large,
            confirmButton = {
                BcPrimaryButton(
                    text = stringResource(Res.string.common_ok),
                    onClick = {
                        onDateRangeChanged(
                            dateRangePickerState.selectedStartDateMillis,
                            dateRangePickerState.selectedEndDateMillis
                        )
                        showDateRangePicker = false
                    },
                )
            },
            dismissButton = {
                BcSecondaryButton(
                    text = stringResource(Res.string.common_cancel),
                    onClick = { showDateRangePicker = false },
                )
            },
            colors = DatePickerDefaults.colors(
                containerColor = MaterialTheme.colorScheme.surfaceContainerHighest,
            )
        ) {
            DateRangePicker(
                state = dateRangePickerState,
                showModeToggle = false,
                colors = DatePickerDefaults.colors(
                    containerColor = MaterialTheme.colorScheme.surfaceContainerHighest,
                )
            )
        }
    }
}

@Composable
private fun AmountStat(
    title: StringResource,
    amount: String,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surfaceContainer, MaterialTheme.shapes.medium)
            .padding(MaterialTheme.dimension.default),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
    ) {
        Text(
            text = stringResource(title),
            style = MaterialTheme.typography.bodyLarge
        )
        Text(
            text = amount,
            style = MaterialTheme.typography.titleLarge,
            color = Green700,
            maxLines = 1,
            overflow = TextOverflow.Visible
        )
    }
}

@Composable
private fun BarbersRevenue(
    barbersRevenue: LazyPagingItems<BarberRevenueUiModel>,
    onBarberClicked: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
    ) {
        if (barbersRevenue.isEmpty().not()) {
            Text(
                text = stringResource(Res.string.for_you_barbers_revenue),
                style = MaterialTheme.typography.titleMedium
            )
        }
        LazyColumn(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
        ) {
            items(barbersRevenue.itemCount) { index ->
                barbersRevenue[index]?.let { barberRevenue ->
                    BarberRevenueItem(
                        barberRevenue = barberRevenue,
                        onClick = { onBarberClicked(barberRevenue.barberId) }
                    )
                }
            }
        }
    }
}

@Composable
fun BarberRevenueItem(
    barberRevenue: BarberRevenueUiModel,
    onClick: () -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(MaterialTheme.shapes.medium)
            .clickable(onClick = onClick)
            .border(
                width = 0.8.dp,
                color = MaterialTheme.colorScheme.surfaceContainer,
                shape = MaterialTheme.shapes.medium
            )
            .padding(MaterialTheme.dimension.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            modifier = Modifier.size(55.dp).clip(CircleShape),
            painter = painterResource(Res.drawable.img_barber1),
            contentDescription = "Barber avatar"
        )
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
        ) {
            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
            ) {
                Text(text = barberRevenue.barberName, style = MaterialTheme.typography.bodyLarge)
                Text(
                    text = stringResource(Res.string.common_total, barberRevenue.totalRevenue),
                    style = MaterialTheme.typography.titleMedium,
                    color = Green700
                )
            }
            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
            ) {
                Text(
                    text = stringResource(Res.string.common_haircuts, barberRevenue.totalHaircuts),
                    style = MaterialTheme.typography.labelMedium
                )
                Text(
                    text = stringResource(Res.string.common_tips, barberRevenue.totalTips),
                    style = MaterialTheme.typography.labelMedium,
                    color = Green700
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview
@Composable
private fun StatsScreenPreview() {
    BarbckerTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
        ) {
            var viewState by remember {
                mutableStateOf(
                    StatsViewState(
                        totalRevenue = "15000.0 $",
                        totalTips = "150.0 $",
                    )
                )
            }
            StatsScreen(
                barbersRevenue = flowOf(
                    PagingData.from(
                        persistentListOf(
                            BarberRevenueUiModel(
                                barberId = "1",
                                barberName = "John",
                                totalHaircuts = 10,
                                totalRevenue = "100.0 $",
                                totalTips = "50.0 $"
                            ),
                            BarberRevenueUiModel(
                                barberId = "2",
                                barberName = "Alex",
                                totalHaircuts = 10,
                                totalRevenue = "100.0 $",
                                totalTips = "50.0$"
                            ),
                            BarberRevenueUiModel(
                                barberId = "3",
                                barberName = "Philipp",
                                totalHaircuts = 10,
                                totalRevenue = "100.0 $",
                                totalTips = "50.0 $"
                            )
                        )
                    )
                ).collectAsLazyPagingItems(),
                viewState = viewState,
                refreshState = rememberPullToRefreshState(),
                onMonthFilterChanged = {
                    viewState = viewState.copy(selectedMonth = it)
                },
                onDateRangeChanged = { startDate, endDate ->
                    if (startDate != null && endDate != null) {
                        viewState = viewState.copy(
                            selectedStartDateMillis = startDate,
                            selectedEndDateMillis = endDate
                        )
                    }
                },
                onBarberClicked = {}
            )
        }
    }
}