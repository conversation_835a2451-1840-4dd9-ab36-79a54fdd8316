package com.zrcoding.barbcker.domain.models

sealed interface Resource<out T, out E : Error> {
    data class Success<out T>(val data: T) : Resource<T, Nothing>
    data class Failure<out E : Error>(val error: E) : Resource<Nothing, E>
}

interface Error

enum class NetworkErrors : Error {
    NO_INTERNET,
    UNKNOWN
}

data class SignInStatus(
    val uid: String,
    val name: String,
    val email: String,
    val phoneNumber: String,
    val completionStatus: CompletionStatus
) {
    enum class CompletionStatus {
        COMPLETED,
        SHOULD_COMPLETE_ACCOUNT,
        SHOULD_SETUP_BARBERS
    }
}

sealed interface SignInErrors : Error {
    data class Network(val error: NetworkErrors) : SignInErrors
    data object InvalidCredentials : SignInErrors
    data object Cancelled : SignInErrors
}

sealed interface ResetPasswordErrors : Error {
    data class Network(val error: NetworkErrors) : ResetPasswordErrors
    data object EmailDoesntExists : ResetPasswordErrors
}

sealed interface SignUpErrors : Error {
    data class Network(val error: NetworkErrors) : SignUpErrors
    data object EmailAlreadyExists : SignUpErrors
}

sealed interface CompleteAccountErrors : Error {
    data class Network(val error: NetworkErrors) : CompleteAccountErrors
    data object ShouldRelogIn : CompleteAccountErrors
}

fun <V, E : Error> success(value: V): Resource<V, E> = Resource.Success(value)

fun <V, E : Error> failure(value: E): Resource<V, E> = Resource.Failure(value)

fun <V, E : Error> Resource<V, E>.getOrNull(): V? = (this as? Resource.Success)?.data

fun <V, E : Error> Resource<V, E>.errorOrNull(): E? = (this as? Resource.Failure)?.error

fun <V, E : Error> Resource<V, E>.isSuccess(): Boolean = this is Resource.Success