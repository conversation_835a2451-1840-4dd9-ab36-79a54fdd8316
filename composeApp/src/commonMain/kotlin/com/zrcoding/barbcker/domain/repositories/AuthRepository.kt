package com.zrcoding.barbcker.domain.repositories

import com.zrcoding.barbcker.domain.models.ResetPasswordErrors
import com.zrcoding.barbcker.domain.models.Resource
import com.zrcoding.barbcker.domain.models.SignInErrors
import com.zrcoding.barbcker.domain.models.SignInStatus
import com.zrcoding.barbcker.domain.models.SignUpErrors

interface AuthRepository {
    suspend fun authenticateWithEmailAndPassword(
        email: String,
        password: String
    ): Resource<SignInStatus, SignInErrors>

    suspend fun authenticateWithGoogle(): Resource<SignInStatus, SignInErrors>

    suspend fun authenticateWithApple(): Resource<SignInStatus, SignInErrors>

    suspend fun signUpWithEmailAndPassword(
        email: String,
        password: String
    ): Resource<Unit, SignUpErrors>

    suspend fun sendPasswordResetEmail(email: String): Resource<Unit, ResetPasswordErrors>
}