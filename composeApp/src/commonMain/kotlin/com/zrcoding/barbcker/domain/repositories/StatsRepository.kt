package com.zrcoding.barbcker.domain.repositories

import app.cash.paging.PagingData
import com.zrcoding.barbcker.domain.models.BarberRevenue
import kotlinx.coroutines.flow.Flow

interface StatsRepository {
    fun getTotalRevenueForPeriod(startTimestamp: Long, endTimestamp: Long): Flow<Double>

    fun getShopOwnerTotalRevenueForPeriod(startTimestamp: Long, endTimestamp: Long): Flow<Double>

    fun getTotalTipsForPeriod(startTimestamp: Long, endTimestamp: Long): Flow<Double>

    fun getBarbersRevenueForPeriod(
        startTimestamp: Long,
        endTimestamp: Long
    ): Flow<PagingData<BarberRevenue>>
}