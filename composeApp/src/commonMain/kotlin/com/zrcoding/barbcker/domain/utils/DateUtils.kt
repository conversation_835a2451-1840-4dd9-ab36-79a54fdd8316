package com.zrcoding.barbcker.domain.utils

import com.zrcoding.barbcker.presentation.features.stats.MonthFilter
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atTime
import kotlinx.datetime.minus
import kotlinx.datetime.number
import kotlinx.datetime.plus
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import kotlin.time.Clock
import kotlin.time.ExperimentalTime

object DateUtils {

    /**
     * Get the start and end timestamps for today (00:00:00 to 23:59:59)
     */
    @OptIn(ExperimentalTime::class)
    fun getTodayRange(): Pair<Long, Long> {
        val now = Clock.System.now()
        val timeZone = TimeZone.UTC
        val today = now.toLocalDateTime(timeZone).date

        val startOfDay = today.atTime(0, 0, 0, 0).toInstant(timeZone)
        val endOfDay = today.atTime(23, 59, 59, 999_999_999).toInstant(timeZone)

        return Pair(startOfDay.toEpochMilliseconds(), endOfDay.toEpochMilliseconds())
    }

    /**
     * Get the start and end timestamps for this month (1st to last day)
     */
    @OptIn(ExperimentalTime::class)
    fun getThisMonthRange(): Pair<Long, Long> {
        val now = Clock.System.now()
        val timeZone = TimeZone.UTC
        val today = now.toLocalDateTime(timeZone).date

        // First day of the month
        val firstDayOfMonth = LocalDate(today.year, today.month, 1)
        // Last day of the month - get first day of next month and subtract 1 day
        val firstDayOfNextMonth = firstDayOfMonth.plus(1, DateTimeUnit.MONTH)
        val lastDayOfMonth = firstDayOfNextMonth.minus(1, DateTimeUnit.DAY)

        val startOfMonth = firstDayOfMonth.atTime(0, 0, 0, 0).toInstant(timeZone)
        val endOfMonth = lastDayOfMonth.atTime(23, 59, 59, 999_999_999).toInstant(timeZone)

        return Pair(startOfMonth.toEpochMilliseconds(), endOfMonth.toEpochMilliseconds())
    }

    /**
     * Get the start and end timestamps for a custom date range
     */
    @OptIn(ExperimentalTime::class)
    fun getCustomRange(startDate: LocalDate, endDate: LocalDate): Pair<Long, Long> {
        val timeZone = TimeZone.currentSystemDefault()

        val startOfRange = startDate.atTime(0, 0, 0, 0).toInstant(timeZone)
        val endOfRange = endDate.atTime(23, 59, 59, 999_999_999).toInstant(timeZone)

        return Pair(startOfRange.toEpochMilliseconds(), endOfRange.toEpochMilliseconds())
    }

    @OptIn(ExperimentalTime::class)
    fun generateMonths(yearsBack: Int = 5): List<MonthFilter> {
        val today = Clock.System.now().toLocalDateTime(TimeZone.UTC).date
        val currentYear = today.year

        return (0 until yearsBack * 12).map { offset ->
            val ym = today.minus(offset, DateTimeUnit.MONTH)
            val monthName = ym.month.name.lowercase().replaceFirstChar { it.titlecase() }

            val label = if (ym.year == currentYear) {
                monthName // e.g. "September"
            } else {
                "$monthName ${ym.year}" // e.g. "August 2024"
            }

            MonthFilter(localDateTime = ym, label = label)
        }
    }

    @OptIn(ExperimentalTime::class)
    fun LocalDate.getStartOfMonth(): Long {
        return LocalDateTime(
            year = year,
            month = month,
            day = 1,
            hour = 0,
            minute = 0,
            second = 0,
            nanosecond = 0
        )
            .toInstant(TimeZone.UTC)
            .toEpochMilliseconds()
    }

    @OptIn(ExperimentalTime::class)
    fun LocalDate.getEndDateOfMonth(): Long {
        val monthLength = when (month.number) {
            1, 3, 5, 7, 8, 10, 12 -> 31
            4, 6, 9, 11 -> 30
            2 -> if (year.isLeapYear()) 29 else 28
            else -> 30 // fallback
        }

        return LocalDateTime(
            year = year,
            month = month,
            day = monthLength,
            hour = 23,
            minute = 59,
            second = 59,
            nanosecond = 999_000_000
        )
            .toInstant(TimeZone.UTC)
            .toEpochMilliseconds()
    }

    fun Int.isLeapYear(): Boolean = (this % 4 == 0 && this % 100 != 0) || (this % 400 == 0)
}
