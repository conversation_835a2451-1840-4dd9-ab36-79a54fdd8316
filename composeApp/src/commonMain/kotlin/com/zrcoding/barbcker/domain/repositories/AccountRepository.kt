package com.zrcoding.barbcker.domain.repositories

import com.tweener.passage.model.Entrant
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.models.CompleteAccountErrors
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.domain.models.NetworkErrors
import com.zrcoding.barbcker.domain.models.Resource
import kotlinx.coroutines.flow.Flow

interface AccountRepository {
    suspend fun saveAccount(entrant: Entrant, shopName: String? = null, currency: Currency? = null)

    suspend fun updateAccount(
        shopName: String,
        currency: Currency
    ): Resource<Unit, CompleteAccountErrors>

    fun getAccount(): Flow<Account>

    suspend fun logout(): Resource<Unit, NetworkErrors>

    suspend fun deleteAccount(): Resource<Unit, NetworkErrors>

    // SETTINGS
    suspend fun isLanguageSelected(): Boolean

    suspend fun setSelectedLanguage()
}