package com.zrcoding.barbcker.domain.utils

import com.zrcoding.barbcker.domain.utils.DateUtils.getEndDateOfMonth
import com.zrcoding.barbcker.domain.utils.DateUtils.getStartOfMonth
import com.zrcoding.barbcker.domain.utils.DateUtils.isLeapYear
import com.zrcoding.barbcker.presentation.features.stats.MonthFilter
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import kotlin.time.ExperimentalTime

@OptIn(ExperimentalTime::class)
class DateUtilsTest {

    @Test
    fun `getTodayRange should return correct start and end of today`() {
        val (startTimestamp, endTimestamp) = DateUtils.getTodayRange()
        
        val startDateTime = kotlinx.datetime.Instant.fromEpochMilliseconds(startTimestamp)
            .toLocalDateTime(TimeZone.UTC)
        val endDateTime = kotlinx.datetime.Instant.fromEpochMilliseconds(endTimestamp)
            .toLocalDateTime(TimeZone.UTC)
        
        // Start should be 00:00:00
        assertEquals(0, startDateTime.hour)
        assertEquals(0, startDateTime.minute)
        assertEquals(0, startDateTime.second)
        assertEquals(0, startDateTime.nanosecond)
        
        // End should be 23:59:59.999999999
        assertEquals(23, endDateTime.hour)
        assertEquals(59, endDateTime.minute)
        assertEquals(59, endDateTime.second)
        assertEquals(999_000_000, endDateTime.nanosecond)
        
        // Both should be the same date
        assertEquals(startDateTime.date, endDateTime.date)
    }

    @Test
    fun `getThisMonthRange should return correct start and end of current month`() {
        val (startTimestamp, endTimestamp) = DateUtils.getThisMonthRange()
        
        val startDateTime = kotlinx.datetime.Instant.fromEpochMilliseconds(startTimestamp)
            .toLocalDateTime(TimeZone.UTC)
        val endDateTime = kotlinx.datetime.Instant.fromEpochMilliseconds(endTimestamp)
            .toLocalDateTime(TimeZone.UTC)
        
        val today = Clock.System.now().toLocalDateTime(TimeZone.UTC).date
        
        // Start should be first day of month at 00:00:00
        assertEquals(1, startDateTime.dayOfMonth)
        assertEquals(today.month, startDateTime.month)
        assertEquals(today.year, startDateTime.year)
        assertEquals(0, startDateTime.hour)
        assertEquals(0, startDateTime.minute)
        assertEquals(0, startDateTime.second)
        
        // End should be last day of month at 23:59:59
        assertEquals(today.month, endDateTime.month)
        assertEquals(today.year, endDateTime.year)
        assertEquals(23, endDateTime.hour)
        assertEquals(59, endDateTime.minute)
        assertEquals(59, endDateTime.second)
    }

    @Test
    fun `getCustomRange should return correct timestamps for given date range`() {
        val startDate = LocalDate(2024, 1, 15)
        val endDate = LocalDate(2024, 1, 20)
        
        val (startTimestamp, endTimestamp) = DateUtils.getCustomRange(startDate, endDate)
        
        val startDateTime = kotlinx.datetime.Instant.fromEpochMilliseconds(startTimestamp)
            .toLocalDateTime(TimeZone.currentSystemDefault())
        val endDateTime = kotlinx.datetime.Instant.fromEpochMilliseconds(endTimestamp)
            .toLocalDateTime(TimeZone.currentSystemDefault())
        
        assertEquals(startDate, startDateTime.date)
        assertEquals(endDate, endDateTime.date)
        
        // Start should be 00:00:00
        assertEquals(0, startDateTime.hour)
        assertEquals(0, startDateTime.minute)
        assertEquals(0, startDateTime.second)
        
        // End should be 23:59:59
        assertEquals(23, endDateTime.hour)
        assertEquals(59, endDateTime.minute)
        assertEquals(59, endDateTime.second)
    }

    @Test
    fun `generateMonths should return correct list of months`() {
        val months = DateUtils.generateMonths(yearsBack = 2)
        
        // Should generate 24 months (2 years * 12 months)
        assertEquals(24, months.size)
        
        // First month should be current month
        val today = Clock.System.now().toLocalDateTime(TimeZone.UTC).date
        val firstMonth = months.first()
        assertEquals(today.month, firstMonth.localDateTime.month)
        assertEquals(today.year, firstMonth.localDateTime.year)
        
        // Check that months are in descending order
        for (i in 0 until months.size - 1) {
            val current = months[i].localDateTime
            val next = months[i + 1].localDateTime
            assertTrue(current >= next, "Months should be in descending order")
        }
    }

    @Test
    fun `generateMonths should format labels correctly for current year`() {
        val months = DateUtils.generateMonths(yearsBack = 1)
        val currentYear = Clock.System.now().toLocalDateTime(TimeZone.UTC).date.year
        
        val currentYearMonths = months.filter { it.localDateTime.year == currentYear }
        
        // Current year months should not include year in label
        currentYearMonths.forEach { month ->
            assertFalse(month.label.contains(currentYear.toString()))
        }
    }

    @Test
    fun `generateMonths should format labels correctly for previous years`() {
        val months = DateUtils.generateMonths(yearsBack = 2)
        val currentYear = Clock.System.now().toLocalDateTime(TimeZone.UTC).date.year
        
        val previousYearMonths = months.filter { it.localDateTime.year != currentYear }
        
        // Previous year months should include year in label
        previousYearMonths.forEach { month ->
            assertTrue(month.label.contains(month.localDateTime.year.toString()))
        }
    }

    @Test
    fun `getStartOfMonth should return correct timestamp`() {
        val date = LocalDate(2024, 3, 15)
        val timestamp = date.getStartOfMonth()
        
        val dateTime = kotlinx.datetime.Instant.fromEpochMilliseconds(timestamp)
            .toLocalDateTime(TimeZone.UTC)
        
        assertEquals(2024, dateTime.year)
        assertEquals(3, dateTime.monthNumber)
        assertEquals(1, dateTime.dayOfMonth)
        assertEquals(0, dateTime.hour)
        assertEquals(0, dateTime.minute)
        assertEquals(0, dateTime.second)
        assertEquals(0, dateTime.nanosecond)
    }

    @Test
    fun `getEndDateOfMonth should return correct timestamp for regular month`() {
        val date = LocalDate(2024, 4, 15) // April has 30 days
        val timestamp = date.getEndDateOfMonth()
        
        val dateTime = kotlinx.datetime.Instant.fromEpochMilliseconds(timestamp)
            .toLocalDateTime(TimeZone.UTC)
        
        assertEquals(2024, dateTime.year)
        assertEquals(4, dateTime.monthNumber)
        assertEquals(30, dateTime.dayOfMonth)
        assertEquals(23, dateTime.hour)
        assertEquals(59, dateTime.minute)
        assertEquals(59, dateTime.second)
        assertEquals(999_000_000, dateTime.nanosecond)
    }

    @Test
    fun `getEndDateOfMonth should return correct timestamp for February in leap year`() {
        val date = LocalDate(2024, 2, 15) // 2024 is a leap year
        val timestamp = date.getEndDateOfMonth()
        
        val dateTime = kotlinx.datetime.Instant.fromEpochMilliseconds(timestamp)
            .toLocalDateTime(TimeZone.UTC)
        
        assertEquals(29, dateTime.dayOfMonth) // February in leap year has 29 days
    }

    @Test
    fun `getEndDateOfMonth should return correct timestamp for February in non-leap year`() {
        val date = LocalDate(2023, 2, 15) // 2023 is not a leap year
        val timestamp = date.getEndDateOfMonth()
        
        val dateTime = kotlinx.datetime.Instant.fromEpochMilliseconds(timestamp)
            .toLocalDateTime(TimeZone.UTC)
        
        assertEquals(28, dateTime.dayOfMonth) // February in non-leap year has 28 days
    }

    @Test
    fun `isLeapYear should correctly identify leap years`() {
        assertTrue(2024.isLeapYear()) // Divisible by 4
        assertTrue(2000.isLeapYear()) // Divisible by 400
        assertFalse(2023.isLeapYear()) // Not divisible by 4
        assertFalse(1900.isLeapYear()) // Divisible by 100 but not 400
    }
}
