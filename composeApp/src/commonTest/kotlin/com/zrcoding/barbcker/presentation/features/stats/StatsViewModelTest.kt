package com.zrcoding.barbcker.presentation.features.stats

import app.cash.turbine.test
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.domain.utils.DateUtils
import com.zrcoding.barbcker.domain.utils.DateUtils.getEndDateOfMonth
import com.zrcoding.barbcker.domain.utils.DateUtils.getStartOfMonth
import com.zrcoding.barbcker.presentation.features.stats.mocks.MockAccountRepository
import com.zrcoding.barbcker.presentation.features.stats.mocks.MockStatsRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atStartOfDayIn
import kotlinx.datetime.atTime
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlin.time.ExperimentalTime

@OptIn(ExperimentalCoroutinesApi::class, ExperimentalTime::class)
class StatsViewModelTest {

    private lateinit var statsRepository: MockStatsRepository
    private lateinit var accountRepository: MockAccountRepository
    private lateinit var viewModel: StatsViewModel
    private val testDispatcher = StandardTestDispatcher()

    @BeforeTest
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        statsRepository = MockStatsRepository()
        accountRepository = MockAccountRepository()
        
        // Set up test data
        statsRepository.setBarbersRevenue(MockStatsRepository.createSampleBarbersRevenue())
        
        viewModel = StatsViewModel(
            statsRepository = statsRepository,
            accountRepository = accountRepository
        )
    }

    @AfterTest
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `initial state should be correctly set`() = runTest {
        val initialState = viewModel.viewState.first()
        
        // Should have months filter populated
        assertTrue(initialState.monthsFilter.isNotEmpty())
        
        // Should have current month selected
        assertNotNull(initialState.selectedMonth)
        
        // Should have current month date range set
        val currentMonthRange = DateUtils.getThisMonthRange()
        assertEquals(currentMonthRange.first, initialState.selectedStartDateMillis)
        assertEquals(currentMonthRange.second, initialState.selectedEndDateMillis)
    }

    @Test
    fun `onMonthFilterChanged should update state and emit period events`() = runTest {
        val testMonth = LocalDate(2024, 3, 1)
        val monthFilter = MonthFilter(
            localDateTime = testMonth,
            label = "March 2024"
        )

        // Get initial state first
        val initialState = viewModel.viewState.first()

        // Call the method
        viewModel.onMonthFilterChanged(monthFilter)
        advanceUntilIdle()

        // Check the updated state
        val updatedState = viewModel.viewState.first()

        assertEquals(monthFilter, updatedState.selectedMonth)
        assertEquals(testMonth.getStartOfMonth(), updatedState.selectedStartDateMillis)
        assertEquals(testMonth.getEndDateOfMonth(), updatedState.selectedEndDateMillis)
    }

    @Test
    fun `onDateRangeChanged should update state with custom date range`() = runTest {
        val startDate = LocalDate(2024, 2, 15)
        val endDate = LocalDate(2024, 2, 20)

        val startTimestamp = startDate.atStartOfDayIn(TimeZone.UTC).toEpochMilliseconds()
        val endTimestamp = endDate.atTime(23, 59, 59, 999_999_999)
            .toInstant(TimeZone.UTC).toEpochMilliseconds()

        // Call the method
        viewModel.onDateRangeChanged(startTimestamp, endTimestamp)
        advanceUntilIdle()

        // Check the updated state
        val updatedState = viewModel.viewState.first()

        assertEquals(startTimestamp, updatedState.selectedStartDateMillis)
        assertEquals(endTimestamp, updatedState.selectedEndDateMillis)
    }

    @Test
    fun `onDateRangeChanged should find matching month filter when dates are in same month`() = runTest {
        val today = Clock.System.now().toLocalDateTime(TimeZone.UTC).date
        val startOfMonth = LocalDate(today.year, today.month, 1)
        val endOfMonth = LocalDate(today.year, today.month, 15) // Same month

        val startTimestamp = startOfMonth.atStartOfDayIn(TimeZone.UTC).toEpochMilliseconds()
        val endTimestamp = endOfMonth.atTime(23, 59, 59, 999_999_999)
            .toInstant(TimeZone.UTC).toEpochMilliseconds()

        // Call the method
        viewModel.onDateRangeChanged(startTimestamp, endTimestamp)
        advanceUntilIdle()

        // Check the updated state
        val updatedState = viewModel.viewState.first()

        // Should find the matching month filter
        assertNotNull(updatedState.selectedMonth)
        assertEquals(today.month, updatedState.selectedMonth?.localDateTime?.month)
    }

    @Test
    fun `onDateRangeChanged should handle null dates gracefully`() = runTest {
        val initialState = viewModel.viewState.first()

        // Test with null start date
        viewModel.onDateRangeChanged(null, **********L)
        advanceUntilIdle()

        // State should not change
        val stateAfterNullStart = viewModel.viewState.first()
        assertEquals(initialState.selectedStartDateMillis, stateAfterNullStart.selectedStartDateMillis)
        assertEquals(initialState.selectedEndDateMillis, stateAfterNullStart.selectedEndDateMillis)

        // Test with null end date
        viewModel.onDateRangeChanged(**********L, null)
        advanceUntilIdle()

        // State should not change
        val stateAfterNullEnd = viewModel.viewState.first()
        assertEquals(initialState.selectedStartDateMillis, stateAfterNullEnd.selectedStartDateMillis)
        assertEquals(initialState.selectedEndDateMillis, stateAfterNullEnd.selectedEndDateMillis)

        // Test with both null
        viewModel.onDateRangeChanged(null, null)
        advanceUntilIdle()

        // State should not change
        val stateAfterBothNull = viewModel.viewState.first()
        assertEquals(initialState.selectedStartDateMillis, stateAfterBothNull.selectedStartDateMillis)
        assertEquals(initialState.selectedEndDateMillis, stateAfterBothNull.selectedEndDateMillis)
    }

    @Test
    fun `barbersRevenue flow should emit data with correct currency formatting`() = runTest {
        // Test with Dollar currency
        accountRepository.setAccount(
            com.zrcoding.barbcker.domain.models.Account.Connected(
                uid = "test",
                email = "<EMAIL>",
                displayName = "Test User",
                phoneNumber = "+**********",
                photoUrl = null,
                shopName = "Test Shop",
                currency = Currency.DOLLAR
            )
        )

        viewModel.barbersRevenue.test {
            val pagingData = awaitItem()
            // Note: Testing PagingData content requires more complex setup
            // This test verifies that the flow emits without errors
        }
    }

    @Test
    fun `barbersRevenue flow should handle Euro currency correctly`() = runTest {
        // Test with Euro currency
        accountRepository.setAccount(
            com.zrcoding.barbcker.domain.models.Account.Connected(
                uid = "test",
                email = "<EMAIL>",
                displayName = "Test User",
                phoneNumber = "+**********",
                photoUrl = null,
                shopName = "Test Shop",
                currency = Currency.EURO
            )
        )

        viewModel.barbersRevenue.test {
            val pagingData = awaitItem()
            // Verify flow emits without errors for Euro currency
        }
    }

    @Test
    fun `months filter should contain current month as first item`() = runTest {
        val state = viewModel.viewState.first()
        val currentMonth = Clock.System.now().toLocalDateTime(TimeZone.UTC).date
        
        assertTrue(state.monthsFilter.isNotEmpty())
        
        val firstMonth = state.monthsFilter.first()
        assertEquals(currentMonth.month, firstMonth.localDateTime.month)
        assertEquals(currentMonth.year, firstMonth.localDateTime.year)
    }

    @Test
    fun `months filter should be in descending chronological order`() = runTest {
        val state = viewModel.viewState.first()
        
        for (i in 0 until state.monthsFilter.size - 1) {
            val current = state.monthsFilter[i].localDateTime
            val next = state.monthsFilter[i + 1].localDateTime
            
            assertTrue(
                current >= next,
                "Months should be in descending order: $current should be >= $next"
            )
        }
    }

    @Test
    fun `selected month should be current month initially`() = runTest {
        val state = viewModel.viewState.first()
        val currentMonth = Clock.System.now().toLocalDateTime(TimeZone.UTC).date
        
        assertNotNull(state.selectedMonth)
        assertEquals(currentMonth.month, state.selectedMonth?.localDateTime?.month)
        assertEquals(currentMonth.year, state.selectedMonth?.localDateTime?.year)
    }
}
