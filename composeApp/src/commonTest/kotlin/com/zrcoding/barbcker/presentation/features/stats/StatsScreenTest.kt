package com.zrcoding.barbcker.presentation.features.stats

import com.zrcoding.barbcker.presentation.features.stats.formatToDisplayedDate
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atStartOfDayIn
import kotlinx.datetime.atTime
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlin.time.ExperimentalTime

@OptIn(ExperimentalTime::class)
class StatsScreenTest {

    @Test
    fun `formatToDisplayedDate should format timestamp correctly`() {
        // Test with a known date: January 15, 2024
        val testDate = LocalDate(2024, 1, 15)
        val timestamp = testDate.atStartOfDayIn(TimeZone.UTC).toEpochMilliseconds()
        
        val formattedDate = formatToDisplayedDate(timestamp)
        
        // Should format as "15 Jan 2024"
        assertTrue(formattedDate.contains("15"))
        assertTrue(formattedDate.contains("Jan"))
        assertTrue(formattedDate.contains("2024"))
    }

    @Test
    fun `formatToDisplayedDate should handle different months correctly`() {
        val testCases = listOf(
            LocalDate(2024, 1, 1) to "Jan",
            LocalDate(2024, 2, 1) to "Feb",
            LocalDate(2024, 3, 1) to "Mar",
            LocalDate(2024, 4, 1) to "Apr",
            LocalDate(2024, 5, 1) to "May",
            LocalDate(2024, 6, 1) to "Jun",
            LocalDate(2024, 7, 1) to "Jul",
            LocalDate(2024, 8, 1) to "Aug",
            LocalDate(2024, 9, 1) to "Sep",
            LocalDate(2024, 10, 1) to "Oct",
            LocalDate(2024, 11, 1) to "Nov",
            LocalDate(2024, 12, 1) to "Dec"
        )
        
        testCases.forEach { (date, expectedMonth) ->
            val timestamp = date.atStartOfDayIn(TimeZone.UTC).toEpochMilliseconds()
            val formattedDate = formatToDisplayedDate(timestamp)
            
            assertTrue(
                formattedDate.contains(expectedMonth),
                "Expected $expectedMonth in formatted date: $formattedDate"
            )
        }
    }

    @Test
    fun `StatsViewState should have correct default values`() {
        val viewState = StatsViewState()
        
        assertEquals(emptyList(), viewState.monthsFilter)
        assertEquals(null, viewState.selectedMonth)
        assertEquals("0.0", viewState.totalRevenue)
        assertEquals("0.0", viewState.shopOwnerTotalRevenue)
        assertEquals("0.0", viewState.totalTips)
        
        // Default dates should be current time
        val now = Clock.System.now().toEpochMilliseconds()
        val timeDifference = kotlin.math.abs(viewState.selectedStartDateMillis - now)
        assertTrue(timeDifference < 1000, "Start date should be close to current time")
        
        val endTimeDifference = kotlin.math.abs(viewState.selectedEndDateMillis - now)
        assertTrue(endTimeDifference < 1000, "End date should be close to current time")
    }

    @Test
    fun `StatsViewState copy should work correctly`() {
        val originalState = StatsViewState(
            totalRevenue = "100.0",
            shopOwnerTotalRevenue = "30.0",
            totalTips = "20.0"
        )
        
        val newMonthFilter = MonthFilter(
            localDateTime = LocalDate(2024, 3, 1),
            label = "March 2024"
        )
        
        val copiedState = originalState.copy(
            selectedMonth = newMonthFilter,
            totalRevenue = "200.0"
        )
        
        assertEquals(newMonthFilter, copiedState.selectedMonth)
        assertEquals("200.0", copiedState.totalRevenue)
        assertEquals("30.0", copiedState.shopOwnerTotalRevenue) // Should remain unchanged
        assertEquals("20.0", copiedState.totalTips) // Should remain unchanged
    }

    @Test
    fun `MonthFilter should store correct data`() {
        val testDate = LocalDate(2024, 6, 15)
        val testLabel = "June 2024"
        
        val monthFilter = MonthFilter(
            localDateTime = testDate,
            label = testLabel
        )
        
        assertEquals(testDate, monthFilter.localDateTime)
        assertEquals(testLabel, monthFilter.label)
    }

    @Test
    fun `MonthFilter equality should work correctly`() {
        val date1 = LocalDate(2024, 3, 1)
        val date2 = LocalDate(2024, 3, 1)
        val date3 = LocalDate(2024, 4, 1)
        
        val filter1 = MonthFilter(date1, "March 2024")
        val filter2 = MonthFilter(date2, "March 2024")
        val filter3 = MonthFilter(date3, "April 2024")
        
        assertEquals(filter1, filter2)
        assertTrue(filter1 != filter3)
    }

    @Test
    fun `formatToDisplayedDate should handle edge cases`() {
        // Test with leap year February 29
        val leapYearDate = LocalDate(2024, 2, 29)
        val timestamp = leapYearDate.atStartOfDayIn(TimeZone.UTC).toEpochMilliseconds()
        val formatted = formatToDisplayedDate(timestamp)
        
        assertTrue(formatted.contains("29"))
        assertTrue(formatted.contains("Feb"))
        assertTrue(formatted.contains("2024"))
    }

    @Test
    fun `formatToDisplayedDate should handle year boundaries`() {
        // Test New Year's Day
        val newYearDate = LocalDate(2024, 1, 1)
        val timestamp = newYearDate.atStartOfDayIn(TimeZone.UTC).toEpochMilliseconds()
        val formatted = formatToDisplayedDate(timestamp)
        
        assertTrue(formatted.contains("1"))
        assertTrue(formatted.contains("Jan"))
        assertTrue(formatted.contains("2024"))
        
        // Test New Year's Eve
        val newYearEveDate = LocalDate(2023, 12, 31)
        val eveTimestamp = newYearEveDate.atStartOfDayIn(TimeZone.UTC).toEpochMilliseconds()
        val eveFormatted = formatToDisplayedDate(eveTimestamp)
        
        assertTrue(eveFormatted.contains("31"))
        assertTrue(eveFormatted.contains("Dec"))
        assertTrue(eveFormatted.contains("2023"))
    }

    @Test
    fun `formatToDisplayedDate should be consistent with different times of day`() {
        val testDate = LocalDate(2024, 5, 15)
        
        // Test start of day
        val startOfDay = testDate.atStartOfDayIn(TimeZone.UTC).toEpochMilliseconds()
        val startFormatted = formatToDisplayedDate(startOfDay)
        
        // Test end of day
        val endOfDay = testDate.atTime(23, 59, 59, 999_999_999)
            .toInstant(TimeZone.UTC).toEpochMilliseconds()
        val endFormatted = formatToDisplayedDate(endOfDay)
        
        // Both should format to the same date string since we only show date, not time
        assertEquals(startFormatted, endFormatted)
        assertTrue(startFormatted.contains("15"))
        assertTrue(startFormatted.contains("May"))
        assertTrue(startFormatted.contains("2024"))
    }
}
