package com.zrcoding.barbcker.presentation.features.stats.mocks

import com.tweener.passage.model.Entrant
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.models.CompleteAccountErrors
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.domain.models.NetworkErrors
import com.zrcoding.barbcker.domain.models.Resource
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

class MockAccountRepository : AccountRepository {

    private var account: Account = Account.Connected(
        uid = "test-uid",
        email = "<EMAIL>",
        displayName = "Test User",
        phoneNumber = "+**********",
        photoUrl = null,
        shopName = "Test Barbershop",
        currency = Currency.DOLLAR
    )

    private var languageSelected: Boolean = true

    fun setAccount(newAccount: Account) {
        account = newAccount
    }

    fun setLanguageSelected(selected: Boolean) {
        languageSelected = selected
    }

    override suspend fun saveAccount(entrant: Entrant, shopName: String?, currency: Currency?) {
        account = Account.Connected(
            uid = entrant.uid,
            email = entrant.email,
            displayName = entrant.displayName,
            phoneNumber = entrant.phoneNumber,
            photoUrl = entrant.photoUrl,
            shopName = shopName ?: "",
            currency = currency ?: Currency.DOLLAR
        )
    }

    override suspend fun updateAccount(
        shopName: String,
        currency: Currency
    ): Resource<Unit, CompleteAccountErrors> {
        if (account is Account.Connected) {
            account = (account as Account.Connected).copy(
                shopName = shopName,
                currency = currency
            )
        }
        return Resource.Success(Unit)
    }

    override fun getAccount(): Flow<Account> {
        return flowOf(account)
    }

    override suspend fun logout(): Resource<Unit, NetworkErrors> {
        account = Account.NotConnected
        return Resource.Success(Unit)
    }

    override suspend fun deleteAccount(): Resource<Unit, NetworkErrors> {
        account = Account.NotConnected
        return Resource.Success(Unit)
    }

    override suspend fun isLanguageSelected(): Boolean {
        return languageSelected
    }

    override suspend fun setSelectedLanguage() {
        languageSelected = true
    }
}
