package com.zrcoding.barbcker.analytics

import com.zrcoding.barbcker.BuildConfig
import org.koin.core.module.Module
import org.koin.dsl.module

class AppConfigImpl : AppConfig {
    override val versionName: String
        get() = BuildConfig.VERSION_NAME

    override val isDebug: Boolean
        get() = BuildConfig.DEBUG
}

actual val platformModule: Module = module {
    single<AppConfig> { AppConfigImpl() }
}