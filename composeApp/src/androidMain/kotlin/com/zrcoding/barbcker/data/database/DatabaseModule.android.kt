package com.zrcoding.barbcker.data.database

import android.content.Context
import androidx.room.Room
import kotlinx.coroutines.Dispatchers
import org.koin.core.module.Module
import org.koin.dsl.module

actual val platformModule: Module = module {
    single<AppDatabase> {
        val appContext: Context = get()
        val dbFile = appContext.getDatabasePath("barbcker.db")
        Room.databaseBuilder<AppDatabase>(appContext, dbFile.absolutePath)
            //.createFromAsset("sqlite.db")
            .fallbackToDestructiveMigration(true)
            .setQueryCoroutineContext(Dispatchers.IO)
            .build()
    }
}