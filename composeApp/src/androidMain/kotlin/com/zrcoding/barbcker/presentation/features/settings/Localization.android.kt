package com.zrcoding.barbcker.presentation.features.settings

import android.content.Context
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import org.koin.core.module.Module
import org.koin.dsl.module

actual class Localization(
    private val context: Context
) {
    actual fun changeLanguage(language: String) {
        val appLocale: LocaleListCompat = LocaleListCompat.forLanguageTags(language)
        // Call this on the main thread as it may require Activity.restart()
        AppCompatDelegate.setApplicationLocales(appLocale)
    }

    actual fun getCurrentLanguage(): String {
        val currentLocales = AppCompatDelegate.getApplicationLocales()
        return if (currentLocales.isEmpty) {
            // Get device locale
            val locale = context.resources.configuration.locales[0]
            locale.language // This will be "fr" for French, etc.
        } else {
            currentLocales[0]?.language ?: "en"
        }
    }
}

actual val settingsModule: Module = module {
    single { Localization(get()) }
}