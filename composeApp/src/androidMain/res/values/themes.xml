<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.Barbcker" parent="@style/Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>

    <style name="Theme.App.Starting" parent="Theme.SplashScreen">
        // Set the splash screen background, animated icon, and animation duration.
        <item name="windowSplashScreenBackground">@color/splash_screen_background</item>

        // Use windowSplashScreenAnimatedIcon to add either a drawable or an
        // animated drawable. One of these is required.
        <item name="windowSplashScreenAnimatedIcon">@drawable/img_logo</item>

        // Set the theme of the Activity that directly follows your splash screen.
        <item name="postSplashScreenTheme">@style/Theme.Barbcker</item>
        # Required.
    </style>
</resources>