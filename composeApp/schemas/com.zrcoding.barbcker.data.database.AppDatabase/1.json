{"formatVersion": 1, "database": {"version": 1, "identityHash": "c3924b298d05a76d89a6682161382af9", "entities": [{"tableName": "barbers", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uuid` TEXT NOT NULL, `name` TEXT NOT NULL, `commission_rate` INTEGER NOT NULL, `phone_number` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL, `is_deleted` INTEGER NOT NULL, PRIMARY KEY(`uuid`))", "fields": [{"fieldPath": "uuid", "columnName": "uuid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "commissionRate", "columnName": "commission_rate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "phoneNumber", "columnName": "phone_number", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updated_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isDeleted", "columnName": "is_deleted", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uuid"]}}, {"tableName": "haircuts", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uuid` TEXT NOT NULL, `price` REAL NOT NULL, `tip` REAL NOT NULL, `commission_rate` INTEGER NOT NULL, `created_at` INTEGER NOT NULL, `barber_uuid` TEXT NOT NULL, PRIMARY KEY(`uuid`))", "fields": [{"fieldPath": "uuid", "columnName": "uuid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "price", "columnName": "price", "affinity": "REAL", "notNull": true}, {"fieldPath": "tip", "columnName": "tip", "affinity": "REAL", "notNull": true}, {"fieldPath": "commissionRate", "columnName": "commission_rate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "barberUuid", "columnName": "barber_uuid", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uuid"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'c3924b298d05a76d89a6682161382af9')"]}}